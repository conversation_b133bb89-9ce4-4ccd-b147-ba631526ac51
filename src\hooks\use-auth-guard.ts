'use client';

import { useEffect, useState } from 'react';
import { useUserStore } from '@/lib/stores/user-store';
import { PermissionUtils } from '@/lib/auth/auth-utils';

export interface AuthGuardOptions {
  requiredPermissions?: string[];
  requiredRole?: string;
  requireEmailVerification?: boolean;
  redirectTo?: string;
  onUnauthorized?: () => void;
}

export interface AuthGuardResult {
  isLoading: boolean;
  isAuthenticated: boolean;
  isAuthorized: boolean;
  hasRequiredPermissions: boolean;
  hasRequiredRole: boolean;
  isEmailVerified: boolean;
  user: any;
  error: string | null;
}

/**
 * Hook for implementing authentication guards
 */
export function useAuthGuard(options: AuthGuardOptions = {}): AuthGuardResult {
  const {
    requiredPermissions = [],
    requiredRole,
    requireEmailVerification = false,
    redirectTo,
    onUnauthorized,
  } = options;

  const {
    isAuthenticated,
    profile,
    isLoading,
    initializeAuth,
  } = useUserStore();

  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Initialize authentication on mount
  useEffect(() => {
    const initialize = async () => {
      try {
        if (!isAuthenticated) {
          await initializeAuth();
        }
      } catch (err) {
        setError('Failed to initialize authentication');
        console.error('Auth initialization error:', err);
      } finally {
        setIsInitializing(false);
      }
    };

    initialize();
  }, [isAuthenticated, initializeAuth]);

  // Check permissions
  const hasRequiredPermissions = profile?.permissions
    ? PermissionUtils.hasAllPermissions(profile.permissions, requiredPermissions)
    : false;

  // Check role
  const hasRequiredRole = requiredRole
    ? profile?.role ? PermissionUtils.hasRole(profile.role, requiredRole) : false
    : true;

  // Check email verification
  const isEmailVerified = requireEmailVerification
    ? profile?.emailVerified === true
    : true;

  // Overall authorization check
  const isAuthorized = isAuthenticated && 
    hasRequiredPermissions && 
    hasRequiredRole && 
    isEmailVerified;

  // Handle unauthorized access
  useEffect(() => {
    if (!isLoading && !isInitializing && !isAuthorized) {
      if (onUnauthorized) {
        onUnauthorized();
      } else if (redirectTo) {
        window.location.href = redirectTo;
      }
    }
  }, [isLoading, isInitializing, isAuthorized, onUnauthorized, redirectTo]);

  return {
    isLoading: isLoading || isInitializing,
    isAuthenticated,
    isAuthorized,
    hasRequiredPermissions,
    hasRequiredRole,
    isEmailVerified,
    user: profile,
    error,
  };
}

/**
 * Hook for checking if user can perform specific actions
 */
export function useCanAccess() {
  const { profile } = useUserStore();

  const canAccess = (options: {
    permissions?: string[];
    role?: string;
    requireEmailVerification?: boolean;
  }): boolean => {
    if (!profile) return false;

    // Check email verification
    if (options.requireEmailVerification && !profile.emailVerified) {
      return false;
    }

    // Check role
    if (options.role && !PermissionUtils.hasRole(profile.role, options.role)) {
      return false;
    }

    // Check permissions
    if (options.permissions && options.permissions.length > 0) {
      if (!profile.permissions || !PermissionUtils.hasAllPermissions(profile.permissions, options.permissions)) {
        return false;
      }
    }

    return true;
  };

  const canAccessEquipment = () => canAccess({
    permissions: ['equipment:read'],
  });

  const canManageEquipment = () => canAccess({
    permissions: ['equipment:write'],
  });

  const canDeleteEquipment = () => canAccess({
    permissions: ['equipment:delete'],
  });

  const canAccessAdmin = () => canAccess({
    role: 'admin',
  });

  const canManageUsers = () => canAccess({
    permissions: ['user:write'],
  });

  const canAccessSystemConfig = () => canAccess({
    permissions: ['system:config'],
  });

  return {
    canAccess,
    canAccessEquipment,
    canManageEquipment,
    canDeleteEquipment,
    canAccessAdmin,
    canManageUsers,
    canAccessSystemConfig,
  };
}

/**
 * Hook for handling authentication redirects
 */
export function useAuthRedirect() {
  const { isAuthenticated, profile } = useUserStore();

  const redirectIfNotAuthenticated = (redirectTo: string = '/auth/login') => {
    if (!isAuthenticated) {
      window.location.href = redirectTo;
      return true;
    }
    return false;
  };

  const redirectIfNotVerified = (redirectTo: string = '/auth/verify-email') => {
    if (isAuthenticated && profile && !profile.emailVerified) {
      window.location.href = redirectTo;
      return true;
    }
    return false;
  };

  const redirectIfNotAuthorized = (
    options: {
      permissions?: string[];
      role?: string;
    },
    redirectTo: string = '/unauthorized'
  ) => {
    if (!isAuthenticated || !profile) {
      window.location.href = '/auth/login';
      return true;
    }

    // Check role
    if (options.role && !PermissionUtils.hasRole(profile.role, options.role)) {
      window.location.href = redirectTo;
      return true;
    }

    // Check permissions
    if (options.permissions && options.permissions.length > 0) {
      if (!profile.permissions || !PermissionUtils.hasAllPermissions(profile.permissions, options.permissions)) {
        window.location.href = redirectTo;
        return true;
      }
    }

    return false;
  };

  return {
    redirectIfNotAuthenticated,
    redirectIfNotVerified,
    redirectIfNotAuthorized,
  };
}

/**
 * Hook for session management
 */
export function useSession() {
  const {
    isAuthenticated,
    profile,
    logout,
    refreshSession,
  } = useUserStore();

  const [sessionExpiry, setSessionExpiry] = useState<Date | null>(null);

  // Monitor session expiry
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const expiresAt = localStorage.getItem('token_expires_at');
      if (expiresAt) {
        setSessionExpiry(new Date(expiresAt));
      }
    }
  }, [isAuthenticated]);

  // Auto-refresh session before expiry
  useEffect(() => {
    if (!sessionExpiry) return;

    const checkSession = () => {
      const now = new Date();
      const timeUntilExpiry = sessionExpiry.getTime() - now.getTime();
      
      // Refresh if expires in the next 5 minutes
      if (timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0) {
        refreshSession();
      }
      
      // Logout if expired
      if (timeUntilExpiry <= 0) {
        logout();
      }
    };

    const interval = setInterval(checkSession, 60 * 1000); // Check every minute
    return () => clearInterval(interval);
  }, [sessionExpiry, refreshSession, logout]);

  const getTimeUntilExpiry = (): number => {
    if (!sessionExpiry) return 0;
    return Math.max(0, sessionExpiry.getTime() - Date.now());
  };

  const isSessionExpiringSoon = (): boolean => {
    const timeUntilExpiry = getTimeUntilExpiry();
    return timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0; // Less than 5 minutes
  };

  return {
    isAuthenticated,
    user: profile,
    sessionExpiry,
    getTimeUntilExpiry,
    isSessionExpiringSoon,
    refreshSession,
    logout,
  };
}
