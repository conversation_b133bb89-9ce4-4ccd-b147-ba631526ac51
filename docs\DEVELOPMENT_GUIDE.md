# Cobalt Mobile Development Guide

## Table of Contents

1. [Getting Started](#getting-started)
2. [Project Architecture](#project-architecture)
3. [Development Workflow](#development-workflow)
4. [Code Standards](#code-standards)
5. [Testing Strategy](#testing-strategy)
6. [Performance Guidelines](#performance-guidelines)
7. [Security Considerations](#security-considerations)
8. [Deployment](#deployment)

## Getting Started

### Prerequisites

- **Node.js**: 18.0.0 or higher
- **pnpm**: 8.0.0 or higher (recommended) or npm/yarn
- **Git**: Latest version
- **VS Code**: Recommended IDE with extensions

### Required VS Code Extensions

```json
{
  "recommendations": [
    "bradlc.vscode-tailwindcss",
    "esbenp.prettier-vscode",
    "ms-vscode.vscode-typescript-next",
    "ms-playwright.playwright",
    "orta.vscode-jest",
    "ms-vscode.vscode-json"
  ]
}
```

### Environment Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-username/cobalt-mobile.git
   cd cobalt-mobile
   ```

2. **Install dependencies**:
   ```bash
   pnpm install
   ```

3. **Set up environment variables**:
   ```bash
   cp .env.example .env.local
   # Edit .env.local with your configuration
   ```

4. **Start development server**:
   ```bash
   pnpm dev
   ```

5. **Run tests**:
   ```bash
   pnpm test
   ```

## Project Architecture

### Directory Structure

```
src/
├── app/                    # Next.js App Router
│   ├── api/               # API routes
│   ├── globals.css        # Global styles
│   ├── layout.tsx         # Root layout
│   └── page.tsx           # Home page
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   ├── equipment/        # Equipment-specific components
│   ├── sequencer/        # Sequencer components
│   └── [feature]/        # Feature-specific components
├── hooks/                # Custom React hooks
├── lib/                  # Utilities and services
│   ├── api/             # API client services
│   ├── stores/          # Zustand stores
│   ├── utils/           # Utility functions
│   └── notifications/   # Notification system
├── styles/              # Additional styles
└── types/               # TypeScript type definitions
```

### Key Architectural Patterns

#### 1. Component Organization

- **UI Components**: Reusable, unstyled components in `components/ui/`
- **Feature Components**: Business logic components organized by feature
- **Page Components**: Top-level components for routes

#### 2. State Management

- **Zustand**: Primary state management with persistence
- **React Query**: Server state management (planned)
- **Local State**: Component-level state with useState/useReducer

#### 3. Data Flow

```
User Interaction → Component → Hook → Store → API → Backend
                                ↓
                            UI Update ← State Change
```

#### 4. Error Handling

- **Error Boundaries**: Catch React component errors
- **API Errors**: Centralized error handling with retry logic
- **User Feedback**: Toast notifications and error states

## Development Workflow

### Branch Strategy

- **main**: Production-ready code
- **develop**: Integration branch for features
- **feature/**: Feature development branches
- **fix/**: Bug fix branches
- **release/**: Release preparation branches

### Commit Convention

Follow [Conventional Commits](https://www.conventionalcommits.org/):

```
type(scope): description

[optional body]

[optional footer]
```

**Types**:
- `feat`: New feature
- `fix`: Bug fix
- `docs`: Documentation changes
- `style`: Code style changes
- `refactor`: Code refactoring
- `test`: Test changes
- `chore`: Build/tooling changes

**Examples**:
```
feat(equipment): add camera temperature monitoring
fix(navigation): resolve mobile swipe gesture issue
docs(api): update equipment endpoint documentation
```

### Pull Request Process

1. **Create feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make changes and commit**:
   ```bash
   git add .
   git commit -m "feat(scope): description"
   ```

3. **Push and create PR**:
   ```bash
   git push origin feature/your-feature-name
   ```

4. **PR Requirements**:
   - [ ] Tests pass
   - [ ] Code review approved
   - [ ] Documentation updated
   - [ ] No merge conflicts

## Code Standards

### TypeScript Guidelines

#### 1. Type Definitions

```typescript
// Use interfaces for object shapes
interface EquipmentSettings {
  gain: number;
  offset: number;
  temperature: number;
}

// Use types for unions and computed types
type EquipmentStatus = 'connected' | 'disconnected' | 'error';
type EquipmentWithStatus = Equipment & { status: EquipmentStatus };

// Use generics for reusable types
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
}
```

#### 2. Function Signatures

```typescript
// Prefer explicit return types for public APIs
export function validateEquipment(equipment: Equipment): ValidationResult {
  // Implementation
}

// Use async/await over Promises
export async function connectEquipment(id: string): Promise<ConnectionResult> {
  // Implementation
}
```

#### 3. Error Handling

```typescript
// Use custom error types
class EquipmentError extends Error {
  constructor(
    message: string,
    public code: string,
    public equipmentId: string
  ) {
    super(message);
    this.name = 'EquipmentError';
  }
}

// Handle errors gracefully
try {
  await connectEquipment(id);
} catch (error) {
  if (error instanceof EquipmentError) {
    // Handle equipment-specific error
  } else {
    // Handle generic error
  }
}
```

### React Guidelines

#### 1. Component Structure

```typescript
interface ComponentProps {
  // Props interface
}

export function Component({ prop1, prop2 }: ComponentProps) {
  // Hooks at the top
  const [state, setState] = useState();
  const { data } = useQuery();
  
  // Event handlers
  const handleClick = useCallback(() => {
    // Implementation
  }, [dependencies]);
  
  // Effects
  useEffect(() => {
    // Side effects
  }, [dependencies]);
  
  // Early returns
  if (loading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;
  
  // Main render
  return (
    <div>
      {/* JSX */}
    </div>
  );
}
```

#### 2. Custom Hooks

```typescript
export function useEquipmentConnection(equipmentId: string) {
  const [status, setStatus] = useState<ConnectionStatus>('disconnected');
  const [error, setError] = useState<string | null>(null);
  
  const connect = useCallback(async () => {
    try {
      setStatus('connecting');
      await equipmentApi.connect(equipmentId);
      setStatus('connected');
    } catch (err) {
      setError(err.message);
      setStatus('error');
    }
  }, [equipmentId]);
  
  return { status, error, connect };
}
```

### Styling Guidelines

#### 1. Tailwind CSS

```typescript
// Use cn() utility for conditional classes
import { cn } from '@/lib/utils';

<button
  className={cn(
    'px-4 py-2 rounded-md font-medium',
    'bg-blue-500 hover:bg-blue-600',
    'disabled:opacity-50 disabled:cursor-not-allowed',
    variant === 'secondary' && 'bg-gray-500 hover:bg-gray-600',
    className
  )}
>
  Button
</button>
```

#### 2. Component Variants

```typescript
import { cva, type VariantProps } from 'class-variance-authority';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md font-medium transition-colors',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
      },
      size: {
        default: 'h-10 px-4 py-2',
        sm: 'h-9 rounded-md px-3',
        lg: 'h-11 rounded-md px-8',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {}

export function Button({ className, variant, size, ...props }: ButtonProps) {
  return (
    <button
      className={cn(buttonVariants({ variant, size, className }))}
      {...props}
    />
  );
}
```

## Testing Strategy

### Test Types

#### 1. Unit Tests

```typescript
// Component testing
import { render, screen } from '@testing-library/react';
import { EquipmentCard } from '../equipment-card';

describe('EquipmentCard', () => {
  it('should display equipment information', () => {
    const equipment = {
      id: 'eq_001',
      name: 'Test Camera',
      type: 'camera',
      status: 'connected',
    };
    
    render(<EquipmentCard equipment={equipment} />);
    
    expect(screen.getByText('Test Camera')).toBeInTheDocument();
    expect(screen.getByText('Connected')).toBeInTheDocument();
  });
});
```

#### 2. Integration Tests

```typescript
// API integration testing
import { equipmentApi } from '@/lib/api/equipment';

describe('Equipment API', () => {
  it('should connect to equipment successfully', async () => {
    const result = await equipmentApi.connectEquipment({
      equipmentId: 'eq_001'
    });
    
    expect(result.success).toBe(true);
    expect(result.data.status).toBe('connected');
  });
});
```

#### 3. E2E Tests

```typescript
// Playwright E2E testing
import { test, expect } from '@playwright/test';

test('equipment connection workflow', async ({ page }) => {
  await page.goto('/equipment');
  
  await page.click('[data-testid="connect-button"]');
  await expect(page.locator('[data-testid="status"]')).toContainText('Connected');
});
```

### Test Commands

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage

# Run E2E tests
pnpm test:e2e

# Run specific test file
pnpm test equipment.test.ts
```

## Performance Guidelines

### 1. Component Optimization

```typescript
// Use React.memo for expensive components
export const ExpensiveComponent = React.memo(({ data }: Props) => {
  return <ComplexVisualization data={data} />;
});

// Use useMemo for expensive calculations
const processedData = useMemo(() => {
  return expensiveDataProcessing(rawData);
}, [rawData]);

// Use useCallback for event handlers
const handleClick = useCallback((id: string) => {
  onItemClick(id);
}, [onItemClick]);
```

### 2. Bundle Optimization

```typescript
// Use dynamic imports for code splitting
const HeavyComponent = lazy(() => import('./heavy-component'));

// Use Next.js dynamic imports
import dynamic from 'next/dynamic';

const DynamicComponent = dynamic(() => import('./component'), {
  loading: () => <LoadingSpinner />,
  ssr: false,
});
```

### 3. Performance Monitoring

```typescript
import { performanceMonitor } from '@/lib/utils/performance-monitor';

// Measure component render time
export function Component() {
  const { measureRender } = usePerformanceMonitor('Component');
  
  useEffect(() => {
    const startTime = performance.now();
    return () => {
      measureRender(performance.now() - startTime);
    };
  });
  
  return <div>Component content</div>;
}
```

## Security Considerations

### 1. Input Validation

```typescript
import { validateObject } from '@/lib/utils/validation';

// Always validate user input
const schema = {
  name: { required: true, type: 'string', maxLength: 100 },
  email: { required: true, type: 'email' },
};

const result = validateObject(userInput, schema);
if (!result.isValid) {
  throw new ValidationError(result.errors);
}
```

### 2. API Security

```typescript
// Rate limiting
import rateLimit from 'express-rate-limit';

const limiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 100, // limit each IP to 100 requests per windowMs
});

// Input sanitization
import DOMPurify from 'dompurify';

const sanitizedInput = DOMPurify.sanitize(userInput);
```

### 3. Environment Variables

```typescript
// Never expose sensitive data to client
// Use NEXT_PUBLIC_ prefix only for public data
const publicApiUrl = process.env.NEXT_PUBLIC_API_URL;
const secretKey = process.env.SECRET_KEY; // Server-side only
```

## Deployment

### Development

```bash
pnpm dev
```

### Production Build

```bash
pnpm build
pnpm start
```

### Docker Deployment

```bash
docker build -t cobalt-mobile .
docker run -p 3000:3000 cobalt-mobile
```

### Environment Configuration

Create appropriate `.env` files for each environment:

- `.env.local` - Local development
- `.env.staging` - Staging environment  
- `.env.production` - Production environment

### Health Checks

The application includes health check endpoints:

```bash
curl http://localhost:3000/api/health
```

## Troubleshooting

### Common Issues

1. **Build Errors**: Check TypeScript errors and dependencies
2. **Test Failures**: Verify mocks and test environment setup
3. **Performance Issues**: Use React DevTools Profiler
4. **Styling Issues**: Check Tailwind CSS configuration

### Debug Tools

- **React DevTools**: Component inspection
- **Redux DevTools**: State management debugging
- **Network Tab**: API request debugging
- **Performance Tab**: Performance profiling

### Getting Help

- Check existing GitHub issues
- Review documentation
- Ask in community Discord
- Create detailed bug reports
