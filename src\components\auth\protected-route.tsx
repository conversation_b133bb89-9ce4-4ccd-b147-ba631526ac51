'use client';

import React, { useEffect, useState } from 'react';
import { useUserStore } from '@/lib/stores/user-store';
import { PermissionUtils, PERMISSIONS, USER_ROLES } from '@/lib/auth/auth-utils';
import { Loader2, Shield, AlertTriangle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

export interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRole?: string;
  requireEmailVerification?: boolean;
  fallback?: React.ReactNode;
  redirectTo?: string;
}

/**
 * Protected Route Component
 * Wraps content that requires authentication and/or specific permissions
 */
export function ProtectedRoute({
  children,
  requiredPermissions = [],
  requiredRole,
  requireEmailVerification = false,
  fallback,
  redirectTo,
}: ProtectedRouteProps) {
  const {
    isAuthenticated,
    profile,
    isLoading,
    initializeAuth,
    logout,
  } = useUserStore();

  const [isInitializing, setIsInitializing] = useState(true);

  // Initialize authentication on mount
  useEffect(() => {
    const initialize = async () => {
      if (!isAuthenticated) {
        await initializeAuth();
      }
      setIsInitializing(false);
    };

    initialize();
  }, [isAuthenticated, initializeAuth]);

  // Show loading state while initializing
  if (isLoading || isInitializing) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Check authentication
  if (!isAuthenticated || !profile) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <Shield className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle>Authentication Required</CardTitle>
            <CardDescription>
              You need to be logged in to access this page.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              className="w-full" 
              onClick={() => {
                if (redirectTo) {
                  window.location.href = redirectTo;
                } else {
                  // Trigger login modal or redirect to login page
                  window.location.href = '/auth/login';
                }
              }}
            >
              Sign In
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check email verification if required
  if (requireEmailVerification && !profile.emailVerified) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-yellow-100">
              <AlertTriangle className="h-6 w-6 text-yellow-600" />
            </div>
            <CardTitle>Email Verification Required</CardTitle>
            <CardDescription>
              Please verify your email address to access this page.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              className="w-full" 
              onClick={() => {
                // Trigger email verification resend
                // This would call the resend verification API
                console.log('Resend verification email');
              }}
            >
              Resend Verification Email
            </Button>
            <Button 
              variant="outline" 
              className="w-full" 
              onClick={logout}
            >
              Sign Out
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check role if required
  if (requiredRole && !PermissionUtils.hasRole(profile.role, requiredRole)) {
    if (fallback) {
      return <>{fallback}</>;
    }

    return (
      <div className="flex items-center justify-center min-h-screen p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
              <Shield className="h-6 w-6 text-red-600" />
            </div>
            <CardTitle>Insufficient Privileges</CardTitle>
            <CardDescription>
              You don't have the required role ({requiredRole}) to access this page.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button 
              variant="outline" 
              className="w-full" 
              onClick={() => window.history.back()}
            >
              Go Back
            </Button>
            <Button 
              variant="outline" 
              className="w-full" 
              onClick={logout}
            >
              Sign Out
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Check permissions if required
  if (requiredPermissions.length > 0) {
    const userPermissions = profile.permissions || [];
    const hasPermission = PermissionUtils.hasAllPermissions(userPermissions, requiredPermissions);

    if (!hasPermission) {
      if (fallback) {
        return <>{fallback}</>;
      }

      return (
        <div className="flex items-center justify-center min-h-screen p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-red-100">
                <Shield className="h-6 w-6 text-red-600" />
              </div>
              <CardTitle>Insufficient Permissions</CardTitle>
              <CardDescription>
                You don't have the required permissions to access this page.
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-sm text-muted-foreground">
                <p className="font-medium">Required permissions:</p>
                <ul className="mt-1 list-disc list-inside">
                  {requiredPermissions.map((permission) => (
                    <li key={permission}>{permission}</li>
                  ))}
                </ul>
              </div>
              <Button 
                variant="outline" 
                className="w-full" 
                onClick={() => window.history.back()}
              >
                Go Back
              </Button>
              <Button 
                variant="outline" 
                className="w-full" 
                onClick={logout}
              >
                Sign Out
              </Button>
            </CardContent>
          </Card>
        </div>
      );
    }
  }

  // All checks passed, render children
  return <>{children}</>;
}

/**
 * Higher-order component for protecting routes
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: Omit<ProtectedRouteProps, 'children'> = {}
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute {...options}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}

/**
 * Hook for checking permissions in components
 */
export function usePermissions() {
  const { profile } = useUserStore();

  const hasPermission = (permission: string): boolean => {
    if (!profile?.permissions) return false;
    return PermissionUtils.hasPermission(profile.permissions, permission);
  };

  const hasAnyPermission = (permissions: string[]): boolean => {
    if (!profile?.permissions) return false;
    return PermissionUtils.hasAnyPermission(profile.permissions, permissions);
  };

  const hasAllPermissions = (permissions: string[]): boolean => {
    if (!profile?.permissions) return false;
    return PermissionUtils.hasAllPermissions(profile.permissions, permissions);
  };

  const hasRole = (role: string): boolean => {
    if (!profile?.role) return false;
    return PermissionUtils.hasRole(profile.role, role);
  };

  const isAdmin = (): boolean => {
    if (!profile?.role) return false;
    return PermissionUtils.isAdmin(profile.role);
  };

  return {
    hasPermission,
    hasAnyPermission,
    hasAllPermissions,
    hasRole,
    isAdmin,
    permissions: profile?.permissions || [],
    role: profile?.role,
  };
}

/**
 * Component for conditionally rendering content based on permissions
 */
export interface PermissionGateProps {
  children: React.ReactNode;
  requiredPermissions?: string[];
  requiredRole?: string;
  fallback?: React.ReactNode;
  requireAll?: boolean; // If true, requires all permissions; if false, requires any
}

export function PermissionGate({
  children,
  requiredPermissions = [],
  requiredRole,
  fallback = null,
  requireAll = true,
}: PermissionGateProps) {
  const { hasAllPermissions, hasAnyPermission, hasRole } = usePermissions();

  // Check role if required
  if (requiredRole && !hasRole(requiredRole)) {
    return <>{fallback}</>;
  }

  // Check permissions if required
  if (requiredPermissions.length > 0) {
    const hasPermission = requireAll
      ? hasAllPermissions(requiredPermissions)
      : hasAnyPermission(requiredPermissions);

    if (!hasPermission) {
      return <>{fallback}</>;
    }
  }

  return <>{children}</>;
}
