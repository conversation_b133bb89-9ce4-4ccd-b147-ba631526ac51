import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/utils/logging';
import { validateObject } from '@/lib/utils/validation';
import { ErrorCodes } from '@/lib/utils/error-handling';

// Mock database - same as in main route
let equipmentDatabase: any[] = [
  {
    id: 'eq_001',
    name: 'ZWO ASI2600MC Pro',
    type: 'camera',
    manufacturer: 'ZWO',
    model: 'ASI2600MC Pro',
    serialNumber: 'ASI2600MC-001',
    status: 'disconnected',
    connectionType: 'usb',
    capabilities: ['cooling', 'gain_control', 'offset_control'],
    settings: {
      gain: 100,
      offset: 10,
      temperature: -10,
      binning: '1x1',
    },
    userId: 'user_001',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

/**
 * GET /api/equipment/[id]
 * Get specific equipment by ID
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    logger.info('Equipment details requested', { equipmentId: id });

    const equipment = equipmentDatabase.find(eq => eq.id === id);
    
    if (!equipment) {
      logger.warn('Equipment not found', { equipmentId: id });
      
      return NextResponse.json({
        success: false,
        error: 'Equipment not found',
        code: ErrorCodes.VALIDATION_ERROR,
      }, { status: 404 });
    }

    // Add simulated real-time data
    const equipmentWithRealTimeData = {
      ...equipment,
      realTimeData: {
        temperature: equipment.type === 'camera' ? -10 + Math.random() * 2 : null,
        power: Math.random() * 100,
        connectionLatency: Math.random() * 50,
        lastHeartbeat: new Date(),
      },
      statistics: {
        totalConnections: Math.floor(Math.random() * 100),
        totalOperatingHours: Math.floor(Math.random() * 1000),
        averageSessionDuration: Math.floor(Math.random() * 180),
        errorRate: Math.random() * 0.05,
      },
    };

    logger.info('Equipment details retrieved', { equipmentId: id });

    return NextResponse.json({
      success: true,
      data: equipmentWithRealTimeData,
      message: 'Equipment details retrieved successfully',
    });

  } catch (error) {
    logger.error('Failed to retrieve equipment details', { 
      equipmentId: params.id,
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve equipment details',
      code: ErrorCodes.NETWORK_ERROR,
    }, { status: 500 });
  }
}

/**
 * PUT /api/equipment/[id]
 * Update specific equipment
 */
export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    const body = await request.json();
    
    logger.info('Updating equipment', { equipmentId: id });

    const equipmentIndex = equipmentDatabase.findIndex(eq => eq.id === id);
    
    if (equipmentIndex === -1) {
      logger.warn('Equipment not found for update', { equipmentId: id });
      
      return NextResponse.json({
        success: false,
        error: 'Equipment not found',
        code: ErrorCodes.VALIDATION_ERROR,
      }, { status: 404 });
    }

    // Validate updates (partial validation)
    const allowedFields = ['name', 'settings', 'capabilities', 'connectionType'];
    const updates = Object.keys(body)
      .filter(key => allowedFields.includes(key))
      .reduce((obj, key) => {
        obj[key] = body[key];
        return obj;
      }, {} as any);

    // Update equipment
    equipmentDatabase[equipmentIndex] = {
      ...equipmentDatabase[equipmentIndex],
      ...updates,
      updatedAt: new Date(),
    };

    logger.info('Equipment updated successfully', { equipmentId: id });

    return NextResponse.json({
      success: true,
      data: equipmentDatabase[equipmentIndex],
      message: 'Equipment updated successfully',
    });

  } catch (error) {
    logger.error('Failed to update equipment', { 
      equipmentId: params.id,
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    return NextResponse.json({
      success: false,
      error: 'Failed to update equipment',
      code: ErrorCodes.NETWORK_ERROR,
    }, { status: 500 });
  }
}

/**
 * DELETE /api/equipment/[id]
 * Delete specific equipment
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;
    
    logger.info('Deleting equipment', { equipmentId: id });

    const equipmentIndex = equipmentDatabase.findIndex(eq => eq.id === id);
    
    if (equipmentIndex === -1) {
      logger.warn('Equipment not found for deletion', { equipmentId: id });
      
      return NextResponse.json({
        success: false,
        error: 'Equipment not found',
        code: ErrorCodes.VALIDATION_ERROR,
      }, { status: 404 });
    }

    // Remove equipment
    const deletedEquipment = equipmentDatabase.splice(equipmentIndex, 1)[0];

    logger.info('Equipment deleted successfully', { equipmentId: id });

    return NextResponse.json({
      success: true,
      data: { id: deletedEquipment.id },
      message: 'Equipment deleted successfully',
    });

  } catch (error) {
    logger.error('Failed to delete equipment', { 
      equipmentId: params.id,
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    return NextResponse.json({
      success: false,
      error: 'Failed to delete equipment',
      code: ErrorCodes.NETWORK_ERROR,
    }, { status: 500 });
  }
}
