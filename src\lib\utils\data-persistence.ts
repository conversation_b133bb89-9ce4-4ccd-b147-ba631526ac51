/**
 * Enhanced Data Persistence Service
 * Provides comprehensive data storage, synchronization, and backup capabilities
 */

import { logger } from './logging';
import { createError, ErrorCodes } from './error-handling';

// Storage types
export type StorageType = 'localStorage' | 'sessionStorage' | 'indexedDB' | 'memory';

// Data persistence options
export interface PersistenceOptions {
  storageType: StorageType;
  key: string;
  version?: number;
  encryption?: boolean;
  compression?: boolean;
  ttl?: number; // Time to live in milliseconds
  syncToCloud?: boolean;
  backupEnabled?: boolean;
}

// Storage adapter interface
interface StorageAdapter {
  get(key: string): Promise<any>;
  set(key: string, value: any, options?: any): Promise<void>;
  remove(key: string): Promise<void>;
  clear(): Promise<void>;
  keys(): Promise<string[]>;
  size(): Promise<number>;
}

// Local storage adapter
class LocalStorageAdapter implements StorageAdapter {
  async get(key: string): Promise<any> {
    try {
      const item = localStorage.getItem(key);
      if (!item) return null;
      
      const parsed = JSON.parse(item);
      
      // Check TTL
      if (parsed.ttl && Date.now() > parsed.ttl) {
        await this.remove(key);
        return null;
      }
      
      return parsed.data;
    } catch (error) {
      logger.warn('Failed to get item from localStorage', { key, error });
      return null;
    }
  }

  async set(key: string, value: any, options?: { ttl?: number }): Promise<void> {
    try {
      const item = {
        data: value,
        timestamp: Date.now(),
        ttl: options?.ttl ? Date.now() + options.ttl : null,
      };
      
      localStorage.setItem(key, JSON.stringify(item));
    } catch (error) {
      logger.error('Failed to set item in localStorage', { key, error });
      throw createError('Storage write failed', {
        code: ErrorCodes.STORAGE_ERROR,
        category: 'system',
        context: { key, storageType: 'localStorage' },
      });
    }
  }

  async remove(key: string): Promise<void> {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      logger.warn('Failed to remove item from localStorage', { key, error });
    }
  }

  async clear(): Promise<void> {
    try {
      localStorage.clear();
    } catch (error) {
      logger.error('Failed to clear localStorage', { error });
    }
  }

  async keys(): Promise<string[]> {
    try {
      return Object.keys(localStorage);
    } catch (error) {
      logger.warn('Failed to get localStorage keys', { error });
      return [];
    }
  }

  async size(): Promise<number> {
    try {
      return Object.keys(localStorage).length;
    } catch (error) {
      logger.warn('Failed to get localStorage size', { error });
      return 0;
    }
  }
}

// IndexedDB adapter
class IndexedDBAdapter implements StorageAdapter {
  private dbName = 'cobalt-mobile-db';
  private version = 1;
  private storeName = 'data';

  private async getDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.version);
      
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(this.storeName)) {
          db.createObjectStore(this.storeName);
        }
      };
    });
  }

  async get(key: string): Promise<any> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.get(key);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => {
          const result = request.result;
          if (!result) {
            resolve(null);
            return;
          }
          
          // Check TTL
          if (result.ttl && Date.now() > result.ttl) {
            this.remove(key);
            resolve(null);
            return;
          }
          
          resolve(result.data);
        };
      });
    } catch (error) {
      logger.warn('Failed to get item from IndexedDB', { key, error });
      return null;
    }
  }

  async set(key: string, value: any, options?: { ttl?: number }): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      const item = {
        data: value,
        timestamp: Date.now(),
        ttl: options?.ttl ? Date.now() + options.ttl : null,
      };
      
      return new Promise((resolve, reject) => {
        const request = store.put(item, key);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      logger.error('Failed to set item in IndexedDB', { key, error });
      throw createError('Storage write failed', {
        code: ErrorCodes.STORAGE_ERROR,
        category: 'system',
        context: { key, storageType: 'indexedDB' },
      });
    }
  }

  async remove(key: string): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.delete(key);
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      logger.warn('Failed to remove item from IndexedDB', { key, error });
    }
  }

  async clear(): Promise<void> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.clear();
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve();
      });
    } catch (error) {
      logger.error('Failed to clear IndexedDB', { error });
    }
  }

  async keys(): Promise<string[]> {
    try {
      const db = await this.getDB();
      const transaction = db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      
      return new Promise((resolve, reject) => {
        const request = store.getAllKeys();
        request.onerror = () => reject(request.error);
        request.onsuccess = () => resolve(request.result as string[]);
      });
    } catch (error) {
      logger.warn('Failed to get IndexedDB keys', { error });
      return [];
    }
  }

  async size(): Promise<number> {
    try {
      const keys = await this.keys();
      return keys.length;
    } catch (error) {
      logger.warn('Failed to get IndexedDB size', { error });
      return 0;
    }
  }
}

// Memory storage adapter (fallback)
class MemoryStorageAdapter implements StorageAdapter {
  private storage = new Map<string, any>();

  async get(key: string): Promise<any> {
    const item = this.storage.get(key);
    if (!item) return null;
    
    // Check TTL
    if (item.ttl && Date.now() > item.ttl) {
      this.storage.delete(key);
      return null;
    }
    
    return item.data;
  }

  async set(key: string, value: any, options?: { ttl?: number }): Promise<void> {
    const item = {
      data: value,
      timestamp: Date.now(),
      ttl: options?.ttl ? Date.now() + options.ttl : null,
    };
    
    this.storage.set(key, item);
  }

  async remove(key: string): Promise<void> {
    this.storage.delete(key);
  }

  async clear(): Promise<void> {
    this.storage.clear();
  }

  async keys(): Promise<string[]> {
    return Array.from(this.storage.keys());
  }

  async size(): Promise<number> {
    return this.storage.size;
  }
}

// Main persistence service
class DataPersistenceService {
  private adapters = new Map<StorageType, StorageAdapter>();

  constructor() {
    // Initialize adapters
    this.adapters.set('localStorage', new LocalStorageAdapter());
    this.adapters.set('indexedDB', new IndexedDBAdapter());
    this.adapters.set('memory', new MemoryStorageAdapter());
  }

  private getAdapter(storageType: StorageType): StorageAdapter {
    const adapter = this.adapters.get(storageType);
    if (!adapter) {
      throw createError(`Storage adapter not found: ${storageType}`, {
        code: ErrorCodes.STORAGE_ERROR,
        category: 'system',
      });
    }
    return adapter;
  }

  // Store data with options
  async store<T>(data: T, options: PersistenceOptions): Promise<void> {
    try {
      const adapter = this.getAdapter(options.storageType);
      
      let processedData = data;
      
      // Apply compression if enabled
      if (options.compression) {
        processedData = await this.compress(processedData);
      }
      
      // Apply encryption if enabled
      if (options.encryption) {
        processedData = await this.encrypt(processedData);
      }
      
      await adapter.set(options.key, processedData, {
        ttl: options.ttl,
      });
      
      logger.info('Data stored successfully', {
        key: options.key,
        storageType: options.storageType,
        size: JSON.stringify(data).length,
      });
      
      // Create backup if enabled
      if (options.backupEnabled) {
        await this.createBackup(options.key, data);
      }
      
    } catch (error) {
      logger.error('Failed to store data', {
        key: options.key,
        storageType: options.storageType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  // Retrieve data
  async retrieve<T>(options: PersistenceOptions): Promise<T | null> {
    try {
      const adapter = this.getAdapter(options.storageType);
      let data = await adapter.get(options.key);
      
      if (!data) return null;
      
      // Apply decryption if enabled
      if (options.encryption) {
        data = await this.decrypt(data);
      }
      
      // Apply decompression if enabled
      if (options.compression) {
        data = await this.decompress(data);
      }
      
      logger.debug('Data retrieved successfully', {
        key: options.key,
        storageType: options.storageType,
      });
      
      return data;
      
    } catch (error) {
      logger.error('Failed to retrieve data', {
        key: options.key,
        storageType: options.storageType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return null;
    }
  }

  // Remove data
  async remove(options: PersistenceOptions): Promise<void> {
    try {
      const adapter = this.getAdapter(options.storageType);
      await adapter.remove(options.key);
      
      logger.info('Data removed successfully', {
        key: options.key,
        storageType: options.storageType,
      });
      
    } catch (error) {
      logger.error('Failed to remove data', {
        key: options.key,
        storageType: options.storageType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Clear all data for storage type
  async clear(storageType: StorageType): Promise<void> {
    try {
      const adapter = this.getAdapter(storageType);
      await adapter.clear();
      
      logger.info('Storage cleared successfully', { storageType });
      
    } catch (error) {
      logger.error('Failed to clear storage', {
        storageType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Get storage statistics
  async getStats(storageType: StorageType): Promise<{
    size: number;
    keys: string[];
    totalSize: number;
  }> {
    try {
      const adapter = this.getAdapter(storageType);
      const keys = await adapter.keys();
      const size = await adapter.size();
      
      // Calculate total size (approximate)
      let totalSize = 0;
      for (const key of keys) {
        const data = await adapter.get(key);
        if (data) {
          totalSize += JSON.stringify(data).length;
        }
      }
      
      return { size, keys, totalSize };
      
    } catch (error) {
      logger.error('Failed to get storage stats', {
        storageType,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return { size: 0, keys: [], totalSize: 0 };
    }
  }

  // Placeholder methods for future implementation
  private async compress(data: any): Promise<any> {
    // TODO: Implement compression
    return data;
  }

  private async decompress(data: any): Promise<any> {
    // TODO: Implement decompression
    return data;
  }

  private async encrypt(data: any): Promise<any> {
    // TODO: Implement encryption
    return data;
  }

  private async decrypt(data: any): Promise<any> {
    // TODO: Implement decryption
    return data;
  }

  private async createBackup(key: string, data: any): Promise<void> {
    // TODO: Implement backup functionality
    logger.debug('Backup created', { key });
  }
}

// Create singleton instance
export const dataPersistence = new DataPersistenceService();

// Convenience functions
export async function storeData<T>(
  key: string,
  data: T,
  storageType: StorageType = 'localStorage',
  options?: Partial<PersistenceOptions>
): Promise<void> {
  return dataPersistence.store(data, {
    key,
    storageType,
    ...options,
  });
}

export async function retrieveData<T>(
  key: string,
  storageType: StorageType = 'localStorage',
  options?: Partial<PersistenceOptions>
): Promise<T | null> {
  return dataPersistence.retrieve<T>({
    key,
    storageType,
    ...options,
  });
}

export async function removeData(
  key: string,
  storageType: StorageType = 'localStorage'
): Promise<void> {
  return dataPersistence.remove({ key, storageType });
}

export default dataPersistence;
