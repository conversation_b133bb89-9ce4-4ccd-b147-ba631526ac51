import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/auth/user-service';
import { JWTUtils } from '@/lib/auth/auth-utils';

/**
 * POST /api/auth/logout
 * Logout user and invalidate tokens
 */
export async function POST(request: NextRequest) {
  try {
    // Get refresh token from request body or cookies
    let refreshToken: string | null = null;
    
    try {
      const body = await request.json();
      refreshToken = body.refreshToken;
    } catch {
      // If no JSON body, try to get from cookies
      refreshToken = request.cookies.get('refresh_token')?.value || null;
    }
    
    // Revoke refresh token if provided
    if (refreshToken) {
      await UserService.revokeRefreshToken(refreshToken);
    }
    
    // Prepare response
    const response = NextResponse.json(
      {
        success: true,
        message: 'Logout successful',
      },
      { status: 200 }
    );
    
    // Clear refresh token cookie
    response.cookies.set('refresh_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/',
    });
    
    // Set security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    
    return response;
    
  } catch (error) {
    console.error('Logout error:', error);
    
    // Even if there's an error, we should still clear the cookie and return success
    // since the client should be logged out regardless
    const response = NextResponse.json(
      {
        success: true,
        message: 'Logout completed',
      },
      { status: 200 }
    );
    
    response.cookies.set('refresh_token', '', {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 0,
      path: '/',
    });
    
    return response;
  }
}

/**
 * OPTIONS /api/auth/logout
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
