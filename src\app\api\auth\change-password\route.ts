import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/auth/user-service';
import { PasswordUtils } from '@/lib/auth/auth-utils';
import { requireAuth, getUserFromHeaders, rateLimit, validateInput, combineMiddleware } from '@/lib/auth/middleware';

// Input validation schema
const changePasswordSchema = {
  currentPassword: { required: true, type: 'string', minLength: 1 },
  newPassword: { required: true, type: 'string', minLength: 8 },
};

/**
 * POST /api/auth/change-password
 * Change user password
 */
export async function POST(request: NextRequest) {
  try {
    // Apply middleware
    const middlewareResult = await combineMiddleware(
      rateLimit({ action: 'default' }),
      requireAuth(),
      validateInput(changePasswordSchema)
    )(request);
    
    if (middlewareResult) {
      return middlewareResult;
    }
    
    // Get user from headers (set by auth middleware)
    const userFromHeaders = getUserFromHeaders(request);
    if (!userFromHeaders) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found in request',
          code: 'USER_NOT_FOUND',
        },
        { status: 404 }
      );
    }
    
    const body = await request.json();
    const { currentPassword, newPassword } = body;
    
    // Validate new password strength
    const passwordValidation = PasswordUtils.validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'New password does not meet requirements',
          code: 'WEAK_PASSWORD',
          details: {
            errors: passwordValidation.errors,
            score: passwordValidation.score,
            requirements: {
              minLength: 8,
              requireUppercase: true,
              requireLowercase: true,
              requireNumbers: true,
              requireSpecialChars: true,
            },
          },
        },
        { status: 400 }
      );
    }
    
    try {
      // Change password
      const success = await UserService.changePassword(
        userFromHeaders.userId,
        currentPassword,
        newPassword
      );
      
      if (!success) {
        return NextResponse.json(
          {
            success: false,
            error: 'Current password is incorrect',
            code: 'INVALID_CURRENT_PASSWORD',
          },
          { status: 400 }
        );
      }
      
      const response = NextResponse.json(
        {
          success: true,
          message: 'Password changed successfully',
        },
        { status: 200 }
      );
      
      // Set security headers
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      
      return response;
      
    } catch (changeError) {
      const errorMessage = changeError instanceof Error ? changeError.message : 'Password change failed';
      
      if (errorMessage.includes('Password validation failed')) {
        return NextResponse.json(
          {
            success: false,
            error: errorMessage,
            code: 'WEAK_PASSWORD',
          },
          { status: 400 }
        );
      }
      
      throw changeError; // Re-throw unexpected errors
    }
    
  } catch (error) {
    console.error('Change password error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Password change failed',
        code: 'CHANGE_PASSWORD_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/change-password
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
