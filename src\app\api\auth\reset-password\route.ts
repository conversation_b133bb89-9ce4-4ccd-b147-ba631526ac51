import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/auth/user-service';
import { rateLimit, validateInput, combineMiddleware } from '@/lib/auth/middleware';

// Input validation schema
const resetRequestSchema = {
  email: { required: true, type: 'email' },
};

/**
 * POST /api/auth/reset-password
 * Request password reset
 */
export async function POST(request: NextRequest) {
  try {
    // Apply middleware
    const middlewareResult = await combineMiddleware(
      rateLimit({ action: 'passwordReset' }),
      validateInput(resetRequestSchema)
    )(request);
    
    if (middlewareResult) {
      return middlewareResult;
    }
    
    const body = await request.json();
    const { email } = body;
    
    // Generate password reset token
    const resetToken = await UserService.generatePasswordResetToken(email);
    
    // Always return success to prevent email enumeration
    // In a real application, you would send an email here
    const response = NextResponse.json(
      {
        success: true,
        message: 'If an account with that email exists, a password reset link has been sent.',
      },
      { status: 200 }
    );
    
    // Log the reset token for development (remove in production)
    if (process.env.NODE_ENV === 'development' && resetToken) {
      console.log(`Password reset token for ${email}: ${resetToken}`);
      console.log(`Reset URL: ${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/auth/reset-password?token=${resetToken}`);
    }
    
    // Set security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    
    return response;
    
  } catch (error) {
    console.error('Password reset request error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Password reset request failed',
        code: 'RESET_REQUEST_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/reset-password
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
