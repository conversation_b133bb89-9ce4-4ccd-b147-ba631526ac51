import { AuthUser, USER_ROLES, ROLE_PERMISSIONS, PasswordUtils, SecurityUtils } from './auth-utils';

// In-memory user database for development
// In production, replace with actual database operations
const users: Map<string, AuthUser & { passwordHash: string; emailVerificationToken?: string; passwordResetToken?: string; passwordResetExpires?: Date }> = new Map();

// Email verification tokens
const emailVerificationTokens: Map<string, { userId: string; expires: Date }> = new Map();

// Password reset tokens
const passwordResetTokens: Map<string, { userId: string; expires: Date }> = new Map();

// Refresh tokens
const refreshTokens: Map<string, { userId: string; expires: Date }> = new Map();

export interface CreateUserData {
  email: string;
  password: string;
  username: string;
  firstName: string;
  lastName: string;
  timezone: string;
}

export interface UpdateUserData {
  firstName?: string;
  lastName?: string;
  username?: string;
  timezone?: string;
  bio?: string;
  location?: string;
}

export class UserService {
  /**
   * Create a new user
   */
  static async createUser(userData: CreateUserData): Promise<AuthUser> {
    const { email, password, username, firstName, lastName, timezone } = userData;
    
    // Check if user already exists
    if (await this.findUserByEmail(email)) {
      throw new Error('User with this email already exists');
    }
    
    if (await this.findUserByUsername(username)) {
      throw new Error('Username is already taken');
    }
    
    // Validate password strength
    const passwordValidation = PasswordUtils.validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
    }
    
    // Hash password
    const passwordHash = await PasswordUtils.hashPassword(password);
    
    // Generate user ID
    const userId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // Create user object
    const user: AuthUser & { passwordHash: string } = {
      id: userId,
      email: email.toLowerCase(),
      username,
      firstName,
      lastName,
      role: USER_ROLES.USER,
      permissions: ROLE_PERMISSIONS[USER_ROLES.USER],
      emailVerified: false,
      twoFactorEnabled: false,
      createdAt: new Date(),
      updatedAt: new Date(),
      passwordHash,
    };
    
    // Store user
    users.set(userId, user);
    
    // Generate email verification token
    const verificationToken = SecurityUtils.generateEmailVerificationToken();
    emailVerificationTokens.set(verificationToken, {
      userId,
      expires: new Date(Date.now() + 24 * 60 * 60 * 1000), // 24 hours
    });
    
    user.emailVerificationToken = verificationToken;
    
    // Return user without password hash
    const { passwordHash: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
  
  /**
   * Find user by email
   */
  static async findUserByEmail(email: string): Promise<(AuthUser & { passwordHash: string }) | null> {
    for (const user of users.values()) {
      if (user.email === email.toLowerCase()) {
        return user;
      }
    }
    return null;
  }
  
  /**
   * Find user by username
   */
  static async findUserByUsername(username: string): Promise<(AuthUser & { passwordHash: string }) | null> {
    for (const user of users.values()) {
      if (user.username === username) {
        return user;
      }
    }
    return null;
  }
  
  /**
   * Find user by ID
   */
  static async findUserById(userId: string): Promise<(AuthUser & { passwordHash: string }) | null> {
    return users.get(userId) || null;
  }
  
  /**
   * Authenticate user with email and password
   */
  static async authenticateUser(email: string, password: string): Promise<AuthUser | null> {
    const user = await this.findUserByEmail(email);
    if (!user) {
      return null;
    }
    
    const isValidPassword = await PasswordUtils.verifyPassword(password, user.passwordHash);
    if (!isValidPassword) {
      return null;
    }
    
    // Return user without password hash
    const { passwordHash: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
  
  /**
   * Update user profile
   */
  static async updateUser(userId: string, updates: UpdateUserData): Promise<AuthUser | null> {
    const user = users.get(userId);
    if (!user) {
      return null;
    }
    
    // Check username uniqueness if updating username
    if (updates.username && updates.username !== user.username) {
      const existingUser = await this.findUserByUsername(updates.username);
      if (existingUser && existingUser.id !== userId) {
        throw new Error('Username is already taken');
      }
    }
    
    // Update user data
    Object.assign(user, {
      ...updates,
      updatedAt: new Date(),
    });
    
    users.set(userId, user);
    
    // Return user without password hash
    const { passwordHash: _, ...userWithoutPassword } = user;
    return userWithoutPassword;
  }
  
  /**
   * Change user password
   */
  static async changePassword(userId: string, currentPassword: string, newPassword: string): Promise<boolean> {
    const user = users.get(userId);
    if (!user) {
      return false;
    }
    
    // Verify current password
    const isValidPassword = await PasswordUtils.verifyPassword(currentPassword, user.passwordHash);
    if (!isValidPassword) {
      return false;
    }
    
    // Validate new password strength
    const passwordValidation = PasswordUtils.validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
    }
    
    // Hash new password
    const newPasswordHash = await PasswordUtils.hashPassword(newPassword);
    
    // Update user
    user.passwordHash = newPasswordHash;
    user.updatedAt = new Date();
    users.set(userId, user);
    
    return true;
  }
  
  /**
   * Verify email with token
   */
  static async verifyEmail(token: string): Promise<boolean> {
    const tokenData = emailVerificationTokens.get(token);
    if (!tokenData || tokenData.expires < new Date()) {
      return false;
    }
    
    const user = users.get(tokenData.userId);
    if (!user) {
      return false;
    }
    
    // Mark email as verified
    user.emailVerified = true;
    user.updatedAt = new Date();
    users.set(user.id, user);
    
    // Remove verification token
    emailVerificationTokens.delete(token);
    
    return true;
  }
  
  /**
   * Generate password reset token
   */
  static async generatePasswordResetToken(email: string): Promise<string | null> {
    const user = await this.findUserByEmail(email);
    if (!user) {
      return null;
    }
    
    const resetToken = SecurityUtils.generatePasswordResetToken();
    passwordResetTokens.set(resetToken, {
      userId: user.id,
      expires: new Date(Date.now() + 60 * 60 * 1000), // 1 hour
    });
    
    return resetToken;
  }
  
  /**
   * Reset password with token
   */
  static async resetPassword(token: string, newPassword: string): Promise<boolean> {
    const tokenData = passwordResetTokens.get(token);
    if (!tokenData || tokenData.expires < new Date()) {
      return false;
    }
    
    const user = users.get(tokenData.userId);
    if (!user) {
      return false;
    }
    
    // Validate new password strength
    const passwordValidation = PasswordUtils.validatePasswordStrength(newPassword);
    if (!passwordValidation.isValid) {
      throw new Error(`Password validation failed: ${passwordValidation.errors.join(', ')}`);
    }
    
    // Hash new password
    const newPasswordHash = await PasswordUtils.hashPassword(newPassword);
    
    // Update user
    user.passwordHash = newPasswordHash;
    user.updatedAt = new Date();
    users.set(user.id, user);
    
    // Remove reset token
    passwordResetTokens.delete(token);
    
    return true;
  }
  
  /**
   * Store refresh token
   */
  static async storeRefreshToken(token: string, userId: string): Promise<void> {
    refreshTokens.set(token, {
      userId,
      expires: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    });
  }
  
  /**
   * Validate refresh token
   */
  static async validateRefreshToken(token: string): Promise<string | null> {
    const tokenData = refreshTokens.get(token);
    if (!tokenData || tokenData.expires < new Date()) {
      if (tokenData) {
        refreshTokens.delete(token);
      }
      return null;
    }
    
    return tokenData.userId;
  }
  
  /**
   * Revoke refresh token
   */
  static async revokeRefreshToken(token: string): Promise<void> {
    refreshTokens.delete(token);
  }
  
  /**
   * Delete user account
   */
  static async deleteUser(userId: string): Promise<boolean> {
    const user = users.get(userId);
    if (!user) {
      return false;
    }
    
    // Remove user
    users.delete(userId);
    
    // Clean up tokens
    for (const [token, data] of emailVerificationTokens.entries()) {
      if (data.userId === userId) {
        emailVerificationTokens.delete(token);
      }
    }
    
    for (const [token, data] of passwordResetTokens.entries()) {
      if (data.userId === userId) {
        passwordResetTokens.delete(token);
      }
    }
    
    for (const [token, data] of refreshTokens.entries()) {
      if (data.userId === userId) {
        refreshTokens.delete(token);
      }
    }
    
    return true;
  }
}

// Initialize with a default admin user for development
if (process.env.NODE_ENV === 'development') {
  UserService.createUser({
    email: '<EMAIL>',
    password: 'Admin123!@#',
    username: 'admin',
    firstName: 'Admin',
    lastName: 'User',
    timezone: 'UTC',
  }).then(user => {
    // Make admin user verified and admin role
    const adminUser = users.get(user.id);
    if (adminUser) {
      adminUser.emailVerified = true;
      adminUser.role = USER_ROLES.ADMIN;
      adminUser.permissions = ROLE_PERMISSIONS[USER_ROLES.ADMIN];
      users.set(user.id, adminUser);
    }
  }).catch(console.error);
}
