'use client';

import React from 'react';
import { Check, X, AlertCircle } from 'lucide-react';
import { cn } from '@/lib/utils';
import { PasswordUtils, PASSWORD_REQUIREMENTS } from '@/lib/auth/auth-utils';

export interface PasswordStrengthProps {
  password: string;
  showRequirements?: boolean;
  className?: string;
}

/**
 * Password Strength Indicator Component
 */
export function PasswordStrength({ 
  password, 
  showRequirements = true, 
  className 
}: PasswordStrengthProps) {
  const validation = PasswordUtils.validatePasswordStrength(password);
  
  const getStrengthColor = (score: number): string => {
    if (score <= 1) return 'bg-red-500';
    if (score <= 2) return 'bg-orange-500';
    if (score <= 3) return 'bg-yellow-500';
    if (score <= 4) return 'bg-blue-500';
    return 'bg-green-500';
  };

  const getStrengthText = (score: number): string => {
    if (score <= 1) return 'Very Weak';
    if (score <= 2) return 'Weak';
    if (score <= 3) return 'Fair';
    if (score <= 4) return 'Good';
    return 'Strong';
  };

  const requirements = [
    {
      text: `At least ${PASSWORD_REQUIREMENTS.minLength} characters`,
      met: password.length >= PASSWORD_REQUIREMENTS.minLength,
    },
    {
      text: 'Contains uppercase letter',
      met: /[A-Z]/.test(password),
    },
    {
      text: 'Contains lowercase letter',
      met: /[a-z]/.test(password),
    },
    {
      text: 'Contains number',
      met: /\d/.test(password),
    },
    {
      text: 'Contains special character',
      met: new RegExp(`[${PASSWORD_REQUIREMENTS.specialChars.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}]`).test(password),
    },
  ];

  if (!password) {
    return null;
  }

  return (
    <div className={cn('space-y-3', className)}>
      {/* Strength Meter */}
      <div className="space-y-2">
        <div className="flex items-center justify-between text-sm">
          <span className="text-muted-foreground">Password Strength</span>
          <span className={cn(
            'font-medium',
            validation.score <= 1 && 'text-red-600',
            validation.score === 2 && 'text-orange-600',
            validation.score === 3 && 'text-yellow-600',
            validation.score === 4 && 'text-blue-600',
            validation.score === 5 && 'text-green-600'
          )}>
            {getStrengthText(validation.score)}
          </span>
        </div>
        
        <div className="flex space-x-1">
          {[1, 2, 3, 4, 5].map((level) => (
            <div
              key={level}
              className={cn(
                'h-2 flex-1 rounded-full transition-colors',
                level <= validation.score
                  ? getStrengthColor(validation.score)
                  : 'bg-muted'
              )}
            />
          ))}
        </div>
      </div>

      {/* Requirements List */}
      {showRequirements && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-muted-foreground">
            Password Requirements
          </h4>
          <ul className="space-y-1">
            {requirements.map((requirement, index) => (
              <li
                key={index}
                className={cn(
                  'flex items-center space-x-2 text-sm',
                  requirement.met ? 'text-green-600' : 'text-muted-foreground'
                )}
              >
                {requirement.met ? (
                  <Check className="h-4 w-4" />
                ) : (
                  <X className="h-4 w-4" />
                )}
                <span>{requirement.text}</span>
              </li>
            ))}
          </ul>
        </div>
      )}

      {/* Error Messages */}
      {validation.errors.length > 0 && (
        <div className="space-y-1">
          {validation.errors.map((error, index) => (
            <div
              key={index}
              className="flex items-center space-x-2 text-sm text-red-600"
            >
              <AlertCircle className="h-4 w-4" />
              <span>{error}</span>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

/**
 * Password Input with Strength Indicator
 */
export interface PasswordInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  showStrength?: boolean;
  showRequirements?: boolean;
  onPasswordChange?: (password: string, validation: ReturnType<typeof PasswordUtils.validatePasswordStrength>) => void;
}

export function PasswordInput({
  showStrength = true,
  showRequirements = true,
  onPasswordChange,
  onChange,
  className,
  ...props
}: PasswordInputProps) {
  const [password, setPassword] = React.useState('');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newPassword = e.target.value;
    setPassword(newPassword);
    
    if (onPasswordChange) {
      const validation = PasswordUtils.validatePasswordStrength(newPassword);
      onPasswordChange(newPassword, validation);
    }
    
    if (onChange) {
      onChange(e);
    }
  };

  return (
    <div className="space-y-3">
      <input
        {...props}
        type="password"
        onChange={handleChange}
        className={cn(
          'flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50',
          className
        )}
      />
      
      {showStrength && (
        <PasswordStrength
          password={password}
          showRequirements={showRequirements}
        />
      )}
    </div>
  );
}

/**
 * Password Generator Component
 */
export interface PasswordGeneratorProps {
  onGenerate: (password: string) => void;
  length?: number;
  className?: string;
}

export function PasswordGenerator({ 
  onGenerate, 
  length = 16, 
  className 
}: PasswordGeneratorProps) {
  const [generatedPassword, setGeneratedPassword] = React.useState('');
  const [isVisible, setIsVisible] = React.useState(false);

  const generatePassword = () => {
    const password = PasswordUtils.generateSecurePassword(length);
    setGeneratedPassword(password);
    setIsVisible(true);
    onGenerate(password);
  };

  const copyToClipboard = async () => {
    if (generatedPassword) {
      try {
        await navigator.clipboard.writeText(generatedPassword);
        // You could add a toast notification here
      } catch (err) {
        console.error('Failed to copy password:', err);
      }
    }
  };

  return (
    <div className={cn('space-y-3', className)}>
      <button
        type="button"
        onClick={generatePassword}
        className="inline-flex items-center px-3 py-2 text-sm font-medium text-primary bg-primary/10 rounded-md hover:bg-primary/20 transition-colors"
      >
        Generate Secure Password
      </button>
      
      {isVisible && generatedPassword && (
        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <input
              type="text"
              value={generatedPassword}
              readOnly
              className="flex-1 h-10 px-3 py-2 text-sm bg-muted rounded-md font-mono"
            />
            <button
              type="button"
              onClick={copyToClipboard}
              className="px-3 py-2 text-sm bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
            >
              Copy
            </button>
          </div>
          
          <PasswordStrength
            password={generatedPassword}
            showRequirements={false}
          />
        </div>
      )}
    </div>
  );
}
