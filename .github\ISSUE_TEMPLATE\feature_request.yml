name: 🚀 Feature Request
description: Suggest a new feature or enhancement for Cobalt Mobile
title: "[Feature]: "
labels: ["enhancement", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for suggesting a new feature! Please fill out the form below with as much detail as possible.

  - type: textarea
    id: summary
    attributes:
      label: Feature Summary
      description: A clear and concise description of the feature you'd like to see.
      placeholder: Briefly describe the feature...
    validations:
      required: true

  - type: textarea
    id: problem
    attributes:
      label: Problem Statement
      description: What problem does this feature solve? What use case does it address?
      placeholder: |
        Is your feature request related to a problem? Please describe.
        A clear and concise description of what the problem is.
    validations:
      required: true

  - type: textarea
    id: solution
    attributes:
      label: Proposed Solution
      description: Describe the solution you'd like to see implemented.
      placeholder: |
        A clear and concise description of what you want to happen.
        Include any specific implementation details if you have them.
    validations:
      required: true

  - type: textarea
    id: alternatives
    attributes:
      label: Alternative Solutions
      description: Describe any alternative solutions or features you've considered.
      placeholder: A clear and concise description of any alternative solutions or features you've considered.

  - type: dropdown
    id: category
    attributes:
      label: Feature Category
      description: Which area of the application does this feature relate to?
      options:
        - Equipment Control
        - Sequence Planning
        - User Interface
        - Performance
        - Accessibility
        - Mobile Experience
        - Data Management
        - Integration
        - Other
    validations:
      required: true

  - type: dropdown
    id: priority
    attributes:
      label: Priority Level
      description: How important is this feature to you?
      options:
        - Low - Nice to have
        - Medium - Would be helpful
        - High - Important for my workflow
        - Critical - Blocking my use of the app
    validations:
      required: true

  - type: dropdown
    id: complexity
    attributes:
      label: Estimated Complexity
      description: How complex do you think this feature would be to implement?
      options:
        - Simple - Small UI change or minor enhancement
        - Medium - New component or moderate functionality
        - Complex - Major feature or significant changes
        - Unknown - Not sure about implementation complexity
    validations:
      required: true

  - type: textarea
    id: mockups
    attributes:
      label: Mockups or Examples
      description: If applicable, add mockups, screenshots, or examples to help explain your feature.
      placeholder: Drag and drop images here or describe the UI/UX...

  - type: textarea
    id: acceptance
    attributes:
      label: Acceptance Criteria
      description: What would need to be true for this feature to be considered complete?
      placeholder: |
        - [ ] Criterion 1
        - [ ] Criterion 2
        - [ ] Criterion 3

  - type: textarea
    id: impact
    attributes:
      label: User Impact
      description: Who would benefit from this feature and how?
      placeholder: |
        - Target users: ...
        - Benefits: ...
        - Use cases: ...

  - type: textarea
    id: technical
    attributes:
      label: Technical Considerations
      description: Any technical requirements, constraints, or considerations?
      placeholder: |
        - Dependencies: ...
        - Performance considerations: ...
        - Compatibility requirements: ...

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context, links, or information about the feature request.
      placeholder: Any additional information...

  - type: checkboxes
    id: terms
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided a clear description of the feature
          required: true
        - label: I understand this is a request and not a guarantee of implementation
          required: true
        - label: I am willing to help test this feature if implemented
          required: false
