# Authentication System Documentation

## Overview

This document provides a comprehensive overview of the authentication and authorization system implemented in the Cobalt Mobile application. The system follows security best practices and provides a complete user authentication flow with role-based access control.

## System Components

### Core Authentication Utilities

- **Password Management**: Secure password hashing, validation, and strength checking
- **JWT Token Handling**: Generation, validation, and refresh of JWT tokens
- **Security Utilities**: Token generation, email verification, password reset
- **Permission System**: Role-based access control with granular permissions

### Server-Side Components

- **Authentication API Routes**: Complete set of endpoints for user authentication
- **Middleware**: Route protection, rate limiting, CSRF protection
- **User Service**: User management, token storage, and verification
- **Input Validation**: Comprehensive validation and sanitization
- **Error Handling**: Secure error responses without information disclosure

### Client-Side Components

- **Protected Routes**: Components for client-side route protection
- **Authentication Guards**: Hooks for implementing authentication checks
- **Password Strength**: Visual indicator and validation for password strength
- **Permission Gates**: Components for conditional rendering based on permissions

## API Endpoints

| Endpoint | Method | Description | Authentication |
|----------|--------|-------------|----------------|
| `/api/auth/login` | POST | User login | None |
| `/api/auth/register` | POST | User registration | None |
| `/api/auth/logout` | POST | User logout | Optional |
| `/api/auth/refresh` | POST | Refresh access token | None |
| `/api/auth/me` | GET | Get current user | Required |
| `/api/auth/verify-email` | POST | Verify email address | None |
| `/api/auth/reset-password` | POST | Request password reset | None |
| `/api/auth/reset-password/confirm` | POST | Confirm password reset | None |
| `/api/auth/change-password` | POST | Change password | Required |

## User Roles and Permissions

The system implements a comprehensive role-based access control (RBAC) system with the following roles:

- **Admin**: Full system access
- **Moderator**: Limited administrative capabilities
- **User**: Standard user access

Permissions are granular and include:

- User management: `user:read`, `user:write`, `user:delete`
- Equipment management: `equipment:read`, `equipment:write`, `equipment:delete`
- System administration: `system:admin`, `system:config`

## Security Features

- **Password Security**:
  - Bcrypt hashing with 12 rounds
  - Comprehensive strength validation
  - Secure password reset flow

- **Token Security**:
  - Short-lived JWT access tokens (15 minutes)
  - Refresh token rotation
  - Token validation and verification

- **Request Security**:
  - Rate limiting for sensitive endpoints
  - CSRF protection
  - Input validation and sanitization
  - Security headers

- **Error Handling**:
  - Secure error messages
  - No information disclosure
  - Comprehensive logging

## Implementation Details

### Password Hashing

```typescript
static async hashPassword(password: string): Promise<string> {
  const saltRounds = 12;
  return bcrypt.hash(password, saltRounds);
}
```

### JWT Token Generation

```typescript
static generateAccessToken(payload: Omit<JWTPayload, 'iat' | 'exp'>): string {
  return jwt.sign(payload, JWT_SECRET, {
    expiresIn: JWT_EXPIRES_IN,
    issuer: 'cobalt-mobile',
    audience: 'cobalt-mobile-app',
  });
}
```

### Authentication Middleware

```typescript
export function requireAuth(options: AuthMiddlewareOptions = {}) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    // Validate token
    const user = await JWTUtils.getUserFromRequest(request);
    
    if (!user) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }
    
    // Check permissions if required
    if (options.requiredPermissions && options.requiredPermissions.length > 0) {
      const hasPermission = PermissionUtils.hasAllPermissions(
        user.permissions,
        options.requiredPermissions
      );
      
      if (!hasPermission) {
        return NextResponse.json(
          { success: false, error: 'Insufficient permissions' },
          { status: 403 }
        );
      }
    }
    
    return null; // Allow request
  };
}
```

### Protected Route Component

```tsx
export function ProtectedRoute({
  children,
  requiredPermissions = [],
  requiredRole,
  requireEmailVerification = false,
}: ProtectedRouteProps) {
  const { isAuthenticated, profile } = useUserStore();

  // Check authentication
  if (!isAuthenticated || !profile) {
    return <AuthenticationRequired />;
  }

  // Check permissions
  if (requiredPermissions.length > 0) {
    const hasPermission = PermissionUtils.hasAllPermissions(
      profile.permissions,
      requiredPermissions
    );

    if (!hasPermission) {
      return <InsufficientPermissions />;
    }
  }

  // All checks passed
  return <>{children}</>;
}
```

## Testing

The authentication system includes comprehensive tests:

- Unit tests for authentication utilities
- Unit tests for user service
- Integration tests for API endpoints
- Component tests for client-side components

Run tests with:

```bash
node src/lib/auth/__tests__/run-tests.js
```

## Security Best Practices

1. **Never store passwords in plain text**
2. **Use httpOnly cookies for refresh tokens in production**
3. **Implement proper CSRF protection**
4. **Rate limit authentication endpoints**
5. **Validate and sanitize all user input**
6. **Use secure headers for all responses**
7. **Implement proper error handling without information disclosure**
8. **Rotate refresh tokens on use**
9. **Implement proper session management**
10. **Use HTTPS in production**

## Dependencies

- `bcryptjs`: Password hashing
- `jsonwebtoken`: JWT token generation and validation
- `isomorphic-dompurify`: Input sanitization

## Manual Installation

If npm install fails, manually install the required dependencies:

```bash
npm install bcryptjs
npm install jsonwebtoken
npm install isomorphic-dompurify
npm install @types/bcryptjs
npm install @types/jsonwebtoken
```

## Environment Variables

The following environment variables should be set in production:

```
JWT_SECRET=your-jwt-secret-key
JWT_REFRESH_SECRET=your-refresh-secret-key
JWT_EXPIRES_IN=15m
JWT_REFRESH_EXPIRES_IN=7d
CSRF_SECRET=your-csrf-secret-key
```

## Future Improvements

1. **Two-Factor Authentication**: Implement 2FA for enhanced security
2. **OAuth Integration**: Add social login options
3. **Session Management**: Implement active sessions list and remote logout
4. **Audit Logging**: Add comprehensive audit logs for security events
5. **User Management UI**: Add admin interface for user management

## Conclusion

The authentication system provides a robust, secure foundation for user authentication and authorization in the Cobalt Mobile application. It follows security best practices and provides a complete user authentication flow with role-based access control.
