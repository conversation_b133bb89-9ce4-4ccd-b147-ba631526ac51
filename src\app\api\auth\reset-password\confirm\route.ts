import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/auth/user-service';
import { PasswordUtils } from '@/lib/auth/auth-utils';
import { rateLimit, validateInput, combineMiddleware } from '@/lib/auth/middleware';

// Input validation schema
const resetConfirmSchema = {
  token: { required: true, type: 'string', minLength: 1 },
  password: { required: true, type: 'string', minLength: 8 },
};

/**
 * POST /api/auth/reset-password/confirm
 * Confirm password reset with token
 */
export async function POST(request: NextRequest) {
  try {
    // Apply middleware
    const middlewareResult = await combineMiddleware(
      rateLimit({ action: 'passwordReset' }),
      validateInput(resetConfirmSchema)
    )(request);
    
    if (middlewareResult) {
      return middlewareResult;
    }
    
    const body = await request.json();
    const { token, password } = body;
    
    // Validate password strength
    const passwordValidation = PasswordUtils.validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Password does not meet requirements',
          code: 'WEAK_PASSWORD',
          details: {
            errors: passwordValidation.errors,
            score: passwordValidation.score,
            requirements: {
              minLength: 8,
              requireUppercase: true,
              requireLowercase: true,
              requireNumbers: true,
              requireSpecialChars: true,
            },
          },
        },
        { status: 400 }
      );
    }
    
    try {
      // Reset password with token
      const success = await UserService.resetPassword(token, password);
      
      if (!success) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid or expired reset token',
            code: 'INVALID_RESET_TOKEN',
          },
          { status: 400 }
        );
      }
      
      const response = NextResponse.json(
        {
          success: true,
          message: 'Password has been reset successfully',
        },
        { status: 200 }
      );
      
      // Set security headers
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      
      return response;
      
    } catch (resetError) {
      const errorMessage = resetError instanceof Error ? resetError.message : 'Password reset failed';
      
      if (errorMessage.includes('Password validation failed')) {
        return NextResponse.json(
          {
            success: false,
            error: errorMessage,
            code: 'WEAK_PASSWORD',
          },
          { status: 400 }
        );
      }
      
      throw resetError; // Re-throw unexpected errors
    }
    
  } catch (error) {
    console.error('Password reset confirmation error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Password reset failed',
        code: 'RESET_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/reset-password/confirm
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
