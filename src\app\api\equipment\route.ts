import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/utils/logging';
import { validateObject } from '@/lib/utils/validation';
import { createError, ErrorCodes } from '@/lib/utils/error-handling';

// Mock database - in production, this would be a real database
let equipmentDatabase: any[] = [
  {
    id: 'eq_001',
    name: 'ZWO ASI2600MC Pro',
    type: 'camera',
    manufacturer: 'ZWO',
    model: 'ASI2600MC Pro',
    serialNumber: 'ASI2600MC-001',
    status: 'disconnected',
    connectionType: 'usb',
    capabilities: ['cooling', 'gain_control', 'offset_control'],
    settings: {
      gain: 100,
      offset: 10,
      temperature: -10,
      binning: '1x1',
    },
    userId: 'user_001',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
  {
    id: 'eq_002',
    name: 'Sky-Watcher EQ6-R Pro',
    type: 'mount',
    manufacturer: 'Sky-Watcher',
    model: 'EQ6-R Pro',
    serialNumber: 'EQ6R-001',
    status: 'disconnected',
    connectionType: 'ethernet',
    capabilities: ['goto', 'tracking', 'autoguiding'],
    settings: {
      slewRate: 'max',
      trackingRate: 'sidereal',
      guidingRate: 0.5,
    },
    userId: 'user_001',
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01'),
  },
];

// Equipment validation schema
const equipmentSchema = {
  name: { required: true, type: 'string', minLength: 1, maxLength: 100 },
  type: { required: true, type: 'string', pattern: /^(camera|mount|focuser|filterwheel|rotator|guider)$/ },
  manufacturer: { required: true, type: 'string', minLength: 1, maxLength: 50 },
  model: { required: true, type: 'string', minLength: 1, maxLength: 50 },
  serialNumber: { type: 'string', maxLength: 50 },
  connectionType: { required: true, type: 'string', pattern: /^(usb|ethernet|serial|wifi)$/ },
  capabilities: { type: 'array', minLength: 0 },
  settings: { type: 'object' },
};

/**
 * GET /api/equipment
 * Get all equipment for the current user
 */
export async function GET(request: NextRequest) {
  try {
    logger.info('Equipment list requested', { 
      url: request.url,
      userAgent: request.headers.get('user-agent') 
    });

    // In production, get userId from authentication
    const userId = 'user_001';
    
    // Filter equipment by user
    const userEquipment = equipmentDatabase.filter(eq => eq.userId === userId);
    
    // Add simulated status updates
    const equipmentWithStatus = userEquipment.map(equipment => ({
      ...equipment,
      lastConnected: equipment.status === 'connected' ? new Date() : null,
      connectionHealth: Math.random() > 0.8 ? 'good' : 'poor',
    }));

    logger.info('Equipment list retrieved', { 
      count: equipmentWithStatus.length,
      userId 
    });

    return NextResponse.json({
      success: true,
      data: equipmentWithStatus,
      message: 'Equipment retrieved successfully',
    });

  } catch (error) {
    logger.error('Failed to retrieve equipment', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    return NextResponse.json({
      success: false,
      error: 'Failed to retrieve equipment',
      code: ErrorCodes.NETWORK_ERROR,
    }, { status: 500 });
  }
}

/**
 * POST /api/equipment
 * Add new equipment
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    logger.info('Adding new equipment', { 
      type: body.type,
      manufacturer: body.manufacturer,
      model: body.model 
    });

    // Validate input
    const validation = validateObject(body, equipmentSchema);
    if (!validation.isValid) {
      logger.warn('Equipment validation failed', { 
        errors: validation.errors 
      });

      return NextResponse.json({
        success: false,
        error: 'Validation failed',
        code: ErrorCodes.VALIDATION_ERROR,
        details: validation.errors,
      }, { status: 400 });
    }

    // Create new equipment
    const newEquipment = {
      id: `eq_${Date.now()}`,
      ...body,
      status: 'disconnected',
      userId: 'user_001', // In production, get from authentication
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // Add to database
    equipmentDatabase.push(newEquipment);

    logger.info('Equipment added successfully', { 
      equipmentId: newEquipment.id,
      type: newEquipment.type 
    });

    return NextResponse.json({
      success: true,
      data: newEquipment,
      message: 'Equipment added successfully',
    }, { status: 201 });

  } catch (error) {
    logger.error('Failed to add equipment', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    return NextResponse.json({
      success: false,
      error: 'Failed to add equipment',
      code: ErrorCodes.NETWORK_ERROR,
    }, { status: 500 });
  }
}

/**
 * PUT /api/equipment
 * Bulk update equipment
 */
export async function PUT(request: NextRequest) {
  try {
    const body = await request.json();
    const { updates } = body;

    if (!Array.isArray(updates)) {
      return NextResponse.json({
        success: false,
        error: 'Updates must be an array',
        code: ErrorCodes.VALIDATION_ERROR,
      }, { status: 400 });
    }

    logger.info('Bulk updating equipment', { count: updates.length });

    const results = [];
    
    for (const update of updates) {
      const { id, ...changes } = update;
      const equipmentIndex = equipmentDatabase.findIndex(eq => eq.id === id);
      
      if (equipmentIndex === -1) {
        results.push({ id, success: false, error: 'Equipment not found' });
        continue;
      }

      // Update equipment
      equipmentDatabase[equipmentIndex] = {
        ...equipmentDatabase[equipmentIndex],
        ...changes,
        updatedAt: new Date(),
      };

      results.push({ id, success: true });
    }

    logger.info('Bulk update completed', { 
      total: updates.length,
      successful: results.filter(r => r.success).length 
    });

    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk update completed',
    });

  } catch (error) {
    logger.error('Failed to bulk update equipment', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    return NextResponse.json({
      success: false,
      error: 'Failed to bulk update equipment',
      code: ErrorCodes.NETWORK_ERROR,
    }, { status: 500 });
  }
}

/**
 * DELETE /api/equipment
 * Bulk delete equipment
 */
export async function DELETE(request: NextRequest) {
  try {
    const body = await request.json();
    const { ids } = body;

    if (!Array.isArray(ids)) {
      return NextResponse.json({
        success: false,
        error: 'IDs must be an array',
        code: ErrorCodes.VALIDATION_ERROR,
      }, { status: 400 });
    }

    logger.info('Bulk deleting equipment', { count: ids.length });

    const results = [];
    
    for (const id of ids) {
      const equipmentIndex = equipmentDatabase.findIndex(eq => eq.id === id);
      
      if (equipmentIndex === -1) {
        results.push({ id, success: false, error: 'Equipment not found' });
        continue;
      }

      // Remove equipment
      equipmentDatabase.splice(equipmentIndex, 1);
      results.push({ id, success: true });
    }

    logger.info('Bulk delete completed', { 
      total: ids.length,
      successful: results.filter(r => r.success).length 
    });

    return NextResponse.json({
      success: true,
      data: results,
      message: 'Bulk delete completed',
    });

  } catch (error) {
    logger.error('Failed to bulk delete equipment', { 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });

    return NextResponse.json({
      success: false,
      error: 'Failed to bulk delete equipment',
      code: ErrorCodes.NETWORK_ERROR,
    }, { status: 500 });
  }
}
