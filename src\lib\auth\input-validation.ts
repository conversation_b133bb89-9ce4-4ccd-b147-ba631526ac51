import DOMPurify from 'isomorphic-dompurify';
import { SecurityUtils, PasswordUtils } from './auth-utils';

// Validation rules
export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'email' | 'username' | 'password' | 'url' | 'phone' | 'number';
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
  sanitize?: boolean;
}

export interface ValidationSchema {
  [key: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string[]>;
  sanitizedData: Record<string, any>;
}

// Common validation patterns
export const VALIDATION_PATTERNS = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  username: /^[a-zA-Z0-9_-]{3,30}$/,
  phone: /^\+?[\d\s\-\(\)]{10,}$/,
  url: /^https?:\/\/.+/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  alphanumericWithSpaces: /^[a-zA-Z0-9\s]+$/,
  noSpecialChars: /^[a-zA-Z0-9\s\-_]+$/,
} as const;

// Sanitization functions
export class InputSanitizer {
  /**
   * Sanitize HTML content
   */
  static sanitizeHtml(input: string): string {
    return DOMPurify.sanitize(input, {
      ALLOWED_TAGS: [],
      ALLOWED_ATTR: [],
    });
  }

  /**
   * Sanitize general text input
   */
  static sanitizeText(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '') // Remove potential HTML tags
      .replace(/[\x00-\x1F\x7F]/g, '') // Remove control characters
      .substring(0, 1000); // Limit length
  }

  /**
   * Sanitize email input
   */
  static sanitizeEmail(input: string): string {
    return input
      .trim()
      .toLowerCase()
      .replace(/[<>]/g, '')
      .substring(0, 254); // RFC 5321 limit
  }

  /**
   * Sanitize username input
   */
  static sanitizeUsername(input: string): string {
    return input
      .trim()
      .replace(/[^a-zA-Z0-9_-]/g, '')
      .substring(0, 30);
  }

  /**
   * Sanitize phone number input
   */
  static sanitizePhone(input: string): string {
    return input
      .trim()
      .replace(/[^+\d\s\-\(\)]/g, '')
      .substring(0, 20);
  }

  /**
   * Sanitize URL input
   */
  static sanitizeUrl(input: string): string {
    return input
      .trim()
      .replace(/[<>]/g, '')
      .substring(0, 2048);
  }

  /**
   * Sanitize numeric input
   */
  static sanitizeNumber(input: string): string {
    return input
      .trim()
      .replace(/[^0-9.-]/g, '');
  }

  /**
   * Remove SQL injection patterns
   */
  static removeSqlInjection(input: string): string {
    const sqlPatterns = [
      /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
      /(--|\/\*|\*\/|;|'|"|`)/g,
      /(\bOR\b|\bAND\b)\s+\d+\s*=\s*\d+/gi,
    ];

    let sanitized = input;
    sqlPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    return sanitized;
  }

  /**
   * Remove XSS patterns
   */
  static removeXss(input: string): string {
    const xssPatterns = [
      /<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,
      /<iframe\b[^<]*(?:(?!<\/iframe>)<[^<]*)*<\/iframe>/gi,
      /javascript:/gi,
      /on\w+\s*=/gi,
      /<\s*\/?\s*(script|iframe|object|embed|form|input|textarea|select|option|button|link|meta|style)\b[^>]*>/gi,
    ];

    let sanitized = input;
    xssPatterns.forEach(pattern => {
      sanitized = sanitized.replace(pattern, '');
    });

    return sanitized;
  }

  /**
   * Comprehensive sanitization
   */
  static sanitizeInput(input: string, type: ValidationRule['type'] = 'string'): string {
    if (typeof input !== 'string') {
      return '';
    }

    // Remove XSS and SQL injection patterns
    let sanitized = this.removeXss(input);
    sanitized = this.removeSqlInjection(sanitized);

    // Apply type-specific sanitization
    switch (type) {
      case 'email':
        return this.sanitizeEmail(sanitized);
      case 'username':
        return this.sanitizeUsername(sanitized);
      case 'phone':
        return this.sanitizePhone(sanitized);
      case 'url':
        return this.sanitizeUrl(sanitized);
      case 'number':
        return this.sanitizeNumber(sanitized);
      default:
        return this.sanitizeText(sanitized);
    }
  }
}

// Enhanced validator class
export class EnhancedValidator {
  /**
   * Validate a single field
   */
  static validateField(value: any, rule: ValidationRule, fieldName: string): string[] {
    const errors: string[] = [];

    // Convert to string for validation
    const stringValue = value?.toString() || '';

    // Required check
    if (rule.required && (!value || stringValue.trim() === '')) {
      errors.push(`${fieldName} is required`);
      return errors;
    }

    // Skip other validations if value is empty and not required
    if (!rule.required && (!value || stringValue.trim() === '')) {
      return errors;
    }

    // Type-specific validation
    switch (rule.type) {
      case 'email':
        if (!VALIDATION_PATTERNS.email.test(stringValue)) {
          errors.push(`${fieldName} must be a valid email address`);
        }
        if (stringValue.length > 254) {
          errors.push(`${fieldName} must be no more than 254 characters`);
        }
        break;

      case 'username':
        if (!VALIDATION_PATTERNS.username.test(stringValue)) {
          errors.push(`${fieldName} must be 3-30 characters and contain only letters, numbers, underscores, and hyphens`);
        }
        break;

      case 'password':
        const passwordValidation = PasswordUtils.validatePasswordStrength(stringValue);
        if (!passwordValidation.isValid) {
          errors.push(...passwordValidation.errors);
        }
        break;

      case 'phone':
        if (!VALIDATION_PATTERNS.phone.test(stringValue)) {
          errors.push(`${fieldName} must be a valid phone number`);
        }
        break;

      case 'url':
        if (!VALIDATION_PATTERNS.url.test(stringValue)) {
          errors.push(`${fieldName} must be a valid URL`);
        }
        break;

      case 'number':
        if (isNaN(Number(stringValue))) {
          errors.push(`${fieldName} must be a valid number`);
        }
        break;
    }

    // Length validation
    if (rule.minLength && stringValue.length < rule.minLength) {
      errors.push(`${fieldName} must be at least ${rule.minLength} characters long`);
    }

    if (rule.maxLength && stringValue.length > rule.maxLength) {
      errors.push(`${fieldName} must be no more than ${rule.maxLength} characters long`);
    }

    // Pattern validation
    if (rule.pattern && !rule.pattern.test(stringValue)) {
      errors.push(`${fieldName} format is invalid`);
    }

    // Custom validation
    if (rule.custom) {
      const customError = rule.custom(value);
      if (customError) {
        errors.push(customError);
      }
    }

    return errors;
  }

  /**
   * Validate an object against a schema
   */
  static validateObject(data: Record<string, any>, schema: ValidationSchema): ValidationResult {
    const errors: Record<string, string[]> = {};
    const sanitizedData: Record<string, any> = {};

    for (const [fieldName, rule] of Object.entries(schema)) {
      const value = data[fieldName];
      
      // Validate field
      const fieldErrors = this.validateField(value, rule, fieldName);
      if (fieldErrors.length > 0) {
        errors[fieldName] = fieldErrors;
      }

      // Sanitize field if requested
      if (rule.sanitize !== false) {
        sanitizedData[fieldName] = typeof value === 'string' 
          ? InputSanitizer.sanitizeInput(value, rule.type)
          : value;
      } else {
        sanitizedData[fieldName] = value;
      }
    }

    return {
      isValid: Object.keys(errors).length === 0,
      errors,
      sanitizedData,
    };
  }

  /**
   * Validate authentication registration data
   */
  static validateRegistration(data: Record<string, any>): ValidationResult {
    const schema: ValidationSchema = {
      email: {
        required: true,
        type: 'email',
        sanitize: true,
      },
      password: {
        required: true,
        type: 'password',
        sanitize: false, // Don't sanitize passwords
      },
      firstName: {
        required: true,
        type: 'string',
        minLength: 1,
        maxLength: 50,
        pattern: VALIDATION_PATTERNS.noSpecialChars,
        sanitize: true,
      },
      lastName: {
        required: true,
        type: 'string',
        minLength: 1,
        maxLength: 50,
        pattern: VALIDATION_PATTERNS.noSpecialChars,
        sanitize: true,
      },
      username: {
        required: true,
        type: 'username',
        sanitize: true,
      },
      timezone: {
        required: true,
        type: 'string',
        minLength: 1,
        maxLength: 50,
        sanitize: true,
      },
    };

    return this.validateObject(data, schema);
  }

  /**
   * Validate authentication login data
   */
  static validateLogin(data: Record<string, any>): ValidationResult {
    const schema: ValidationSchema = {
      email: {
        required: true,
        type: 'email',
        sanitize: true,
      },
      password: {
        required: true,
        type: 'string',
        minLength: 1,
        sanitize: false, // Don't sanitize passwords
      },
    };

    return this.validateObject(data, schema);
  }

  /**
   * Validate password change data
   */
  static validatePasswordChange(data: Record<string, any>): ValidationResult {
    const schema: ValidationSchema = {
      currentPassword: {
        required: true,
        type: 'string',
        minLength: 1,
        sanitize: false,
      },
      newPassword: {
        required: true,
        type: 'password',
        sanitize: false,
      },
    };

    return this.validateObject(data, schema);
  }
}
