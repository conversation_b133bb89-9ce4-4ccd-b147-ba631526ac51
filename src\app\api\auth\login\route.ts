import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/auth/user-service';
import { JWTUtils, SecurityUtils } from '@/lib/auth/auth-utils';
import { rateLimit, validateInput, combineMiddleware } from '@/lib/auth/middleware';

// Input validation schema
const loginSchema = {
  email: { required: true, type: 'email' },
  password: { required: true, type: 'string', minLength: 1 },
  rememberMe: { required: false, type: 'boolean' },
};

/**
 * POST /api/auth/login
 * Authenticate user with email and password
 */
export async function POST(request: NextRequest) {
  try {
    // Apply middleware
    const middlewareResult = await combineMiddleware(
      rateLimit({ action: 'login' }),
      validateInput(loginSchema)
    )(request);
    
    if (middlewareResult) {
      return middlewareResult;
    }
    
    const body = await request.json();
    const { email, password, rememberMe = false } = body;
    
    // Authenticate user
    const user = await UserService.authenticateUser(email, password);
    
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid email or password',
          code: 'INVALID_CREDENTIALS',
        },
        { status: 401 }
      );
    }
    
    // Generate tokens
    const accessToken = JWTUtils.generateAccessToken({
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
    });
    
    const refreshToken = JWTUtils.generateRefreshToken(user.id);
    
    // Store refresh token
    await UserService.storeRefreshToken(refreshToken, user.id);
    
    // Calculate expiration time
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
    
    // Prepare response
    const response = NextResponse.json(
      {
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            avatar: user.avatar,
            bio: user.bio,
            location: user.location,
            timezone: user.timezone,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
          token: accessToken,
          refreshToken,
          expiresAt: expiresAt.toISOString(),
          permissions: user.permissions,
        },
        message: 'Login successful',
      },
      { status: 200 }
    );
    
    // Set secure cookies if remember me is enabled
    if (rememberMe) {
      response.cookies.set('refresh_token', refreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
      });
    }
    
    // Set security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    
    return response;
    
  } catch (error) {
    console.error('Login error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Login failed',
        code: 'LOGIN_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/login
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
