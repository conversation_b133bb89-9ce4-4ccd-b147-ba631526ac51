import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/auth/user-service';
import { JWTUtils, PasswordUtils } from '@/lib/auth/auth-utils';
import { rateLimit, validateInput, combineMiddleware } from '@/lib/auth/middleware';

// Input validation schema
const registerSchema = {
  email: { required: true, type: 'email' },
  password: { required: true, type: 'string', minLength: 8 },
  firstName: { required: true, type: 'string', minLength: 1, maxLength: 50 },
  lastName: { required: true, type: 'string', minLength: 1, maxLength: 50 },
  username: { required: true, type: 'username' },
  timezone: { required: true, type: 'string', minLength: 1 },
};

/**
 * POST /api/auth/register
 * Register a new user account
 */
export async function POST(request: NextRequest) {
  try {
    // Apply middleware
    const middlewareResult = await combineMiddleware(
      rateLimit({ action: 'register' }),
      validateInput(registerSchema)
    )(request);
    
    if (middlewareResult) {
      return middlewareResult;
    }
    
    const body = await request.json();
    const { email, password, firstName, lastName, username, timezone } = body;
    
    // Additional password validation
    const passwordValidation = PasswordUtils.validatePasswordStrength(password);
    if (!passwordValidation.isValid) {
      return NextResponse.json(
        {
          success: false,
          error: 'Password does not meet requirements',
          code: 'WEAK_PASSWORD',
          details: {
            errors: passwordValidation.errors,
            score: passwordValidation.score,
            requirements: {
              minLength: 8,
              requireUppercase: true,
              requireLowercase: true,
              requireNumbers: true,
              requireSpecialChars: true,
            },
          },
        },
        { status: 400 }
      );
    }
    
    try {
      // Create user
      const user = await UserService.createUser({
        email,
        password,
        firstName,
        lastName,
        username,
        timezone,
      });
      
      // Generate tokens for auto-login
      const accessToken = JWTUtils.generateAccessToken({
        userId: user.id,
        email: user.email,
        role: user.role,
        permissions: user.permissions,
      });
      
      const refreshToken = JWTUtils.generateRefreshToken(user.id);
      
      // Store refresh token
      await UserService.storeRefreshToken(refreshToken, user.id);
      
      // Calculate expiration time
      const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
      
      // Prepare response
      const response = NextResponse.json(
        {
          success: true,
          data: {
            user: {
              id: user.id,
              email: user.email,
              username: user.username,
              firstName: user.firstName,
              lastName: user.lastName,
              avatar: user.avatar,
              bio: user.bio,
              location: user.location,
              timezone: user.timezone,
              createdAt: user.createdAt,
              updatedAt: user.updatedAt,
            },
            token: accessToken,
            refreshToken,
            expiresAt: expiresAt.toISOString(),
            permissions: user.permissions,
          },
          message: 'Registration successful. Please check your email to verify your account.',
        },
        { status: 201 }
      );
      
      // Set security headers
      response.headers.set('X-Content-Type-Options', 'nosniff');
      response.headers.set('X-Frame-Options', 'DENY');
      response.headers.set('X-XSS-Protection', '1; mode=block');
      
      return response;
      
    } catch (userError) {
      const errorMessage = userError instanceof Error ? userError.message : 'Registration failed';
      
      // Handle specific user creation errors
      if (errorMessage.includes('email already exists')) {
        return NextResponse.json(
          {
            success: false,
            error: 'An account with this email address already exists',
            code: 'EMAIL_EXISTS',
          },
          { status: 409 }
        );
      }
      
      if (errorMessage.includes('username is already taken')) {
        return NextResponse.json(
          {
            success: false,
            error: 'This username is already taken',
            code: 'USERNAME_EXISTS',
          },
          { status: 409 }
        );
      }
      
      if (errorMessage.includes('Password validation failed')) {
        return NextResponse.json(
          {
            success: false,
            error: errorMessage,
            code: 'WEAK_PASSWORD',
          },
          { status: 400 }
        );
      }
      
      throw userError; // Re-throw unexpected errors
    }
    
  } catch (error) {
    console.error('Registration error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Registration failed',
        code: 'REGISTRATION_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/register
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
