/**
 * @jest-environment jsdom
 */

import {
  PasswordUtils,
  JWTUtils,
  SecurityUtils,
  PermissionUtils,
  PASSWORD_REQUIREMENTS,
  USER_ROLES,
  PERMISSIONS,
} from '../auth-utils';

// Mock environment variables
process.env.JWT_SECRET = 'test-jwt-secret';
process.env.JWT_REFRESH_SECRET = 'test-refresh-secret';

describe('PasswordUtils', () => {
  describe('validatePasswordStrength', () => {
    it('should validate strong password', () => {
      const result = PasswordUtils.validatePasswordStrength('StrongP@ssw0rd123');
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(result.score).toBeGreaterThan(3);
    });

    it('should reject weak password', () => {
      const result = PasswordUtils.validatePasswordStrength('weak');
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.score).toBeLessThan(3);
    });

    it('should require minimum length', () => {
      const result = PasswordUtils.validatePasswordStrength('Short1!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(`Password must be at least ${PASSWORD_REQUIREMENTS.minLength} characters long`);
    });

    it('should require uppercase letter', () => {
      const result = PasswordUtils.validatePasswordStrength('lowercase123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one uppercase letter');
    });

    it('should require lowercase letter', () => {
      const result = PasswordUtils.validatePasswordStrength('UPPERCASE123!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one lowercase letter');
    });

    it('should require number', () => {
      const result = PasswordUtils.validatePasswordStrength('NoNumbers!');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one number');
    });

    it('should require special character', () => {
      const result = PasswordUtils.validatePasswordStrength('NoSpecialChars123');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Password must contain at least one special character');
    });

    it('should reject password that is too long', () => {
      const longPassword = 'a'.repeat(PASSWORD_REQUIREMENTS.maxLength + 1);
      const result = PasswordUtils.validatePasswordStrength(longPassword);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain(`Password must be no more than ${PASSWORD_REQUIREMENTS.maxLength} characters long`);
    });
  });

  describe('hashPassword and verifyPassword', () => {
    it('should hash and verify password correctly', async () => {
      const password = 'TestPassword123!';
      const hash = await PasswordUtils.hashPassword(password);
      
      expect(hash).toBeDefined();
      expect(hash).not.toBe(password);
      
      const isValid = await PasswordUtils.verifyPassword(password, hash);
      expect(isValid).toBe(true);
      
      const isInvalid = await PasswordUtils.verifyPassword('WrongPassword', hash);
      expect(isInvalid).toBe(false);
    });
  });

  describe('generateSecurePassword', () => {
    it('should generate password with default length', () => {
      const password = PasswordUtils.generateSecurePassword();
      expect(password).toHaveLength(16);
    });

    it('should generate password with custom length', () => {
      const password = PasswordUtils.generateSecurePassword(20);
      expect(password).toHaveLength(20);
    });

    it('should generate strong password', () => {
      const password = PasswordUtils.generateSecurePassword();
      const validation = PasswordUtils.validatePasswordStrength(password);
      expect(validation.isValid).toBe(true);
      expect(validation.score).toBeGreaterThan(3);
    });

    it('should generate different passwords each time', () => {
      const password1 = PasswordUtils.generateSecurePassword();
      const password2 = PasswordUtils.generateSecurePassword();
      expect(password1).not.toBe(password2);
    });
  });
});

describe('JWTUtils', () => {
  const mockPayload = {
    userId: 'user123',
    email: '<EMAIL>',
    role: USER_ROLES.USER,
    permissions: [PERMISSIONS.USER_READ],
  };

  describe('generateAccessToken and verifyAccessToken', () => {
    it('should generate and verify access token', () => {
      const token = JWTUtils.generateAccessToken(mockPayload);
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      
      const decoded = JWTUtils.verifyAccessToken(token);
      expect(decoded).toBeDefined();
      expect(decoded?.userId).toBe(mockPayload.userId);
      expect(decoded?.email).toBe(mockPayload.email);
      expect(decoded?.role).toBe(mockPayload.role);
    });

    it('should return null for invalid token', () => {
      const decoded = JWTUtils.verifyAccessToken('invalid-token');
      expect(decoded).toBeNull();
    });
  });

  describe('generateRefreshToken and verifyRefreshToken', () => {
    it('should generate and verify refresh token', () => {
      const token = JWTUtils.generateRefreshToken(mockPayload.userId);
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      
      const decoded = JWTUtils.verifyRefreshToken(token);
      expect(decoded).toBeDefined();
      expect(decoded?.userId).toBe(mockPayload.userId);
    });

    it('should return null for invalid refresh token', () => {
      const decoded = JWTUtils.verifyRefreshToken('invalid-token');
      expect(decoded).toBeNull();
    });
  });

  describe('extractTokenFromHeader', () => {
    it('should extract token from Bearer header', () => {
      const token = 'test-token';
      const authHeader = `Bearer ${token}`;
      const extracted = JWTUtils.extractTokenFromHeader(authHeader);
      expect(extracted).toBe(token);
    });

    it('should return null for invalid header format', () => {
      const extracted = JWTUtils.extractTokenFromHeader('Invalid header');
      expect(extracted).toBeNull();
    });

    it('should return null for null header', () => {
      const extracted = JWTUtils.extractTokenFromHeader(null);
      expect(extracted).toBeNull();
    });
  });
});

describe('SecurityUtils', () => {
  describe('generateSecureToken', () => {
    it('should generate token with default length', () => {
      const token = SecurityUtils.generateSecureToken();
      expect(token).toHaveLength(64); // 32 bytes = 64 hex chars
    });

    it('should generate token with custom length', () => {
      const token = SecurityUtils.generateSecureToken(16);
      expect(token).toHaveLength(32); // 16 bytes = 32 hex chars
    });

    it('should generate different tokens each time', () => {
      const token1 = SecurityUtils.generateSecureToken();
      const token2 = SecurityUtils.generateSecureToken();
      expect(token1).not.toBe(token2);
    });
  });

  describe('hashToken and verifyToken', () => {
    it('should hash and verify token correctly', () => {
      const token = 'test-token';
      const hash = SecurityUtils.hashToken(token);
      
      expect(hash).toBeDefined();
      expect(hash).not.toBe(token);
      
      const isValid = SecurityUtils.verifyToken(token, hash);
      expect(isValid).toBe(true);
      
      const isInvalid = SecurityUtils.verifyToken('wrong-token', hash);
      expect(isInvalid).toBe(false);
    });
  });

  describe('isValidEmail', () => {
    it('should validate correct email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];
      
      validEmails.forEach(email => {
        expect(SecurityUtils.isValidEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email addresses', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'test@',
        '<EMAIL>',
        'a'.repeat(255) + '@example.com', // Too long
      ];
      
      invalidEmails.forEach(email => {
        expect(SecurityUtils.isValidEmail(email)).toBe(false);
      });
    });
  });

  describe('isValidUsername', () => {
    it('should validate correct usernames', () => {
      const validUsernames = [
        'user123',
        'test_user',
        'user-name',
        'abc',
        'a'.repeat(30),
      ];
      
      validUsernames.forEach(username => {
        expect(SecurityUtils.isValidUsername(username)).toBe(true);
      });
    });

    it('should reject invalid usernames', () => {
      const invalidUsernames = [
        'ab', // Too short
        'a'.repeat(31), // Too long
        'user@name', // Invalid character
        'user name', // Space not allowed
        'user.name', // Dot not allowed
      ];
      
      invalidUsernames.forEach(username => {
        expect(SecurityUtils.isValidUsername(username)).toBe(false);
      });
    });
  });

  describe('sanitizeInput', () => {
    it('should remove HTML tags', () => {
      const input = '<script>alert("xss")</script>Hello';
      const sanitized = SecurityUtils.sanitizeInput(input);
      expect(sanitized).toBe('Hello');
    });

    it('should trim whitespace', () => {
      const input = '  Hello World  ';
      const sanitized = SecurityUtils.sanitizeInput(input);
      expect(sanitized).toBe('Hello World');
    });

    it('should limit length', () => {
      const input = 'a'.repeat(2000);
      const sanitized = SecurityUtils.sanitizeInput(input);
      expect(sanitized.length).toBeLessThanOrEqual(1000);
    });
  });
});

describe('PermissionUtils', () => {
  const userPermissions = [PERMISSIONS.USER_READ, PERMISSIONS.EQUIPMENT_READ];

  describe('hasPermission', () => {
    it('should return true for existing permission', () => {
      const result = PermissionUtils.hasPermission(userPermissions, PERMISSIONS.USER_READ);
      expect(result).toBe(true);
    });

    it('should return false for missing permission', () => {
      const result = PermissionUtils.hasPermission(userPermissions, PERMISSIONS.USER_DELETE);
      expect(result).toBe(false);
    });
  });

  describe('hasAnyPermission', () => {
    it('should return true if user has any of the required permissions', () => {
      const required = [PERMISSIONS.USER_READ, PERMISSIONS.USER_DELETE];
      const result = PermissionUtils.hasAnyPermission(userPermissions, required);
      expect(result).toBe(true);
    });

    it('should return false if user has none of the required permissions', () => {
      const required = [PERMISSIONS.USER_DELETE, PERMISSIONS.SYSTEM_ADMIN];
      const result = PermissionUtils.hasAnyPermission(userPermissions, required);
      expect(result).toBe(false);
    });
  });

  describe('hasAllPermissions', () => {
    it('should return true if user has all required permissions', () => {
      const required = [PERMISSIONS.USER_READ, PERMISSIONS.EQUIPMENT_READ];
      const result = PermissionUtils.hasAllPermissions(userPermissions, required);
      expect(result).toBe(true);
    });

    it('should return false if user is missing any required permission', () => {
      const required = [PERMISSIONS.USER_READ, PERMISSIONS.USER_DELETE];
      const result = PermissionUtils.hasAllPermissions(userPermissions, required);
      expect(result).toBe(false);
    });
  });

  describe('getPermissionsForRole', () => {
    it('should return correct permissions for admin role', () => {
      const permissions = PermissionUtils.getPermissionsForRole(USER_ROLES.ADMIN);
      expect(permissions).toContain(PERMISSIONS.SYSTEM_ADMIN);
      expect(permissions).toContain(PERMISSIONS.USER_DELETE);
    });

    it('should return correct permissions for user role', () => {
      const permissions = PermissionUtils.getPermissionsForRole(USER_ROLES.USER);
      expect(permissions).toContain(PERMISSIONS.USER_READ);
      expect(permissions).not.toContain(PERMISSIONS.SYSTEM_ADMIN);
    });

    it('should return empty array for unknown role', () => {
      const permissions = PermissionUtils.getPermissionsForRole('unknown-role');
      expect(permissions).toEqual([]);
    });
  });

  describe('hasRole', () => {
    it('should return true for matching role', () => {
      const result = PermissionUtils.hasRole(USER_ROLES.ADMIN, USER_ROLES.ADMIN);
      expect(result).toBe(true);
    });

    it('should return false for different role', () => {
      const result = PermissionUtils.hasRole(USER_ROLES.USER, USER_ROLES.ADMIN);
      expect(result).toBe(false);
    });
  });

  describe('isAdmin', () => {
    it('should return true for admin role', () => {
      const result = PermissionUtils.isAdmin(USER_ROLES.ADMIN);
      expect(result).toBe(true);
    });

    it('should return false for non-admin role', () => {
      const result = PermissionUtils.isAdmin(USER_ROLES.USER);
      expect(result).toBe(false);
    });
  });
});
