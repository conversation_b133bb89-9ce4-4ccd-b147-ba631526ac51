# Cobalt Mobile API Reference

## Overview

The Cobalt Mobile API provides comprehensive endpoints for managing astrophotography equipment, sequences, and monitoring. All endpoints return JSON responses with a consistent structure.

## Base URL

```
http://localhost:3000/api
```

## Response Format

All API responses follow this consistent structure:

```typescript
interface ApiResponse<T> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
  code?: string;
  details?: any;
  meta?: {
    pagination?: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
    };
    timestamp: string;
    version: string;
  };
}
```

## Authentication

Currently, the API uses a simplified authentication system for development. In production, implement proper JWT or OAuth2 authentication.

## Error Handling

The API uses standard HTTP status codes and provides detailed error information:

- `200` - Success
- `201` - Created
- `400` - Bad Request (validation errors)
- `401` - Unauthorized
- `403` - Forbidden
- `404` - Not Found
- `500` - Internal Server Error

Error responses include:
- `error`: Human-readable error message
- `code`: Machine-readable error code
- `details`: Additional error context (for validation errors)

## Equipment Management

### Get All Equipment

```http
GET /api/equipment
```

**Description**: Retrieve all equipment for the current user.

**Response**:
```json
{
  "success": true,
  "data": [
    {
      "id": "eq_001",
      "name": "ZWO ASI2600MC Pro",
      "type": "camera",
      "manufacturer": "ZWO",
      "model": "ASI2600MC Pro",
      "serialNumber": "ASI2600MC-001",
      "status": "disconnected",
      "connectionType": "usb",
      "capabilities": ["cooling", "gain_control", "offset_control"],
      "settings": {
        "gain": 100,
        "offset": 10,
        "temperature": -10,
        "binning": "1x1"
      },
      "userId": "user_001",
      "createdAt": "2024-01-01T00:00:00.000Z",
      "updatedAt": "2024-01-01T00:00:00.000Z",
      "lastConnected": null,
      "connectionHealth": "good"
    }
  ],
  "message": "Equipment retrieved successfully"
}
```

### Add New Equipment

```http
POST /api/equipment
```

**Description**: Add new equipment to the user's collection.

**Request Body**:
```json
{
  "name": "New Camera",
  "type": "camera",
  "manufacturer": "ZWO",
  "model": "ASI2600MC Pro",
  "serialNumber": "ASI2600MC-002",
  "connectionType": "usb",
  "capabilities": ["cooling", "gain_control"],
  "settings": {
    "gain": 100,
    "offset": 10
  }
}
```

**Validation Rules**:
- `name`: Required, 1-100 characters
- `type`: Required, one of: camera, mount, focuser, filterwheel, rotator, guider
- `manufacturer`: Required, 1-50 characters
- `model`: Required, 1-50 characters
- `serialNumber`: Optional, max 50 characters
- `connectionType`: Required, one of: usb, ethernet, serial, wifi
- `capabilities`: Optional array
- `settings`: Optional object

**Response**: `201 Created` with the created equipment object.

### Get Equipment by ID

```http
GET /api/equipment/{id}
```

**Description**: Get detailed information about specific equipment.

**Parameters**:
- `id` (path): Equipment ID

**Response**:
```json
{
  "success": true,
  "data": {
    "id": "eq_001",
    "name": "ZWO ASI2600MC Pro",
    // ... other equipment fields
    "realTimeData": {
      "temperature": -9.8,
      "power": 85.2,
      "connectionLatency": 23,
      "lastHeartbeat": "2024-01-01T12:00:00.000Z"
    },
    "statistics": {
      "totalConnections": 45,
      "totalOperatingHours": 234,
      "averageSessionDuration": 120,
      "errorRate": 0.02
    }
  }
}
```

### Update Equipment

```http
PUT /api/equipment/{id}
```

**Description**: Update specific equipment settings.

**Parameters**:
- `id` (path): Equipment ID

**Request Body**: Partial equipment object with fields to update.

**Response**: `200 OK` with updated equipment object.

### Delete Equipment

```http
DELETE /api/equipment/{id}
```

**Description**: Delete specific equipment.

**Parameters**:
- `id` (path): Equipment ID

**Response**: `200 OK` with confirmation.

### Bulk Operations

#### Bulk Update Equipment

```http
PUT /api/equipment
```

**Request Body**:
```json
{
  "updates": [
    {
      "id": "eq_001",
      "name": "Updated Camera Name",
      "settings": { "gain": 120 }
    },
    {
      "id": "eq_002",
      "name": "Updated Mount Name"
    }
  ]
}
```

**Response**:
```json
{
  "success": true,
  "data": [
    { "id": "eq_001", "success": true },
    { "id": "eq_002", "success": true }
  ]
}
```

#### Bulk Delete Equipment

```http
DELETE /api/equipment
```

**Request Body**:
```json
{
  "ids": ["eq_001", "eq_002"]
}
```

## Equipment Connection Management

### Connect Equipment

```http
POST /api/equipment/{id}/connect
```

**Description**: Establish connection to equipment.

**Parameters**:
- `id` (path): Equipment ID

**Request Body**:
```json
{
  "timeout": 5000,
  "retries": 3
}
```

**Response**:
```json
{
  "success": true,
  "data": {
    "equipmentId": "eq_001",
    "status": "connected",
    "connectionTime": 2340,
    "connectedAt": "2024-01-01T12:00:00.000Z"
  }
}
```

### Disconnect Equipment

```http
DELETE /api/equipment/{id}/connect
```

**Description**: Disconnect from equipment.

**Response**:
```json
{
  "success": true,
  "data": {
    "equipmentId": "eq_001",
    "status": "disconnected",
    "disconnectedAt": "2024-01-01T12:05:00.000Z"
  }
}
```

### Get Connection Status

```http
GET /api/equipment/{id}/connect
```

**Description**: Get current connection status and metrics.

**Response**:
```json
{
  "success": true,
  "data": {
    "equipmentId": "eq_001",
    "status": "connected",
    "timestamp": "2024-01-01T12:00:00.000Z",
    "signalStrength": 95.2,
    "latency": 23,
    "dataRate": 856.3
  }
}
```

## Health Check

### Application Health

```http
GET /api/health
```

**Description**: Get application health status and system information.

**Response**:
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "uptime": 3600,
  "version": "1.0.0",
  "environment": "development",
  "memory": {
    "used": 45,
    "total": 128,
    "external": 12
  },
  "system": {
    "platform": "linux",
    "arch": "x64",
    "nodeVersion": "v20.0.0"
  }
}
```

## Rate Limiting

The API implements rate limiting to prevent abuse:

- **General endpoints**: 100 requests per minute per IP
- **Equipment connection**: 10 requests per minute per equipment
- **Health check**: 60 requests per minute per IP

Rate limit headers are included in responses:
- `X-RateLimit-Limit`: Request limit per window
- `X-RateLimit-Remaining`: Remaining requests in current window
- `X-RateLimit-Reset`: Time when the rate limit resets

## Pagination

For endpoints that return lists, pagination is supported:

**Query Parameters**:
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `sort`: Sort field (default: createdAt)
- `order`: Sort order (asc/desc, default: desc)

**Response Meta**:
```json
{
  "meta": {
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8
    }
  }
}
```

## Filtering and Search

Many endpoints support filtering and search:

**Query Parameters**:
- `search`: Text search across relevant fields
- `type`: Filter by equipment type
- `status`: Filter by status
- `manufacturer`: Filter by manufacturer

Example:
```http
GET /api/equipment?search=camera&type=camera&status=connected
```

## WebSocket Events

For real-time updates, the API supports WebSocket connections:

**Connection**: `ws://localhost:3000/api/ws`

**Event Types**:
- `equipment.status.changed`
- `equipment.connected`
- `equipment.disconnected`
- `equipment.error`

**Event Format**:
```json
{
  "type": "equipment.status.changed",
  "timestamp": "2024-01-01T12:00:00.000Z",
  "data": {
    "equipmentId": "eq_001",
    "oldStatus": "disconnected",
    "newStatus": "connected"
  }
}
```

## SDK and Client Libraries

Official client libraries are available for:
- JavaScript/TypeScript (included in this project)
- Python (planned)
- C# (planned)

## Changelog

### v1.0.0 (Current)
- Initial API release
- Equipment management endpoints
- Connection management
- Health monitoring
- Rate limiting
- WebSocket support

## Examples

### JavaScript/TypeScript Client

```typescript
import { equipmentApi } from '@/lib/api/equipment';

// Get all equipment
const equipment = await equipmentApi.getEquipment();

// Add new equipment
const newCamera = await equipmentApi.addEquipment({
  name: 'My Camera',
  type: 'camera',
  manufacturer: 'ZWO',
  model: 'ASI2600MC Pro',
  connectionType: 'usb',
});

// Connect to equipment
await equipmentApi.connectEquipment({ equipmentId: 'eq_001' });
```

### cURL Examples

```bash
# Get equipment list
curl -X GET http://localhost:3000/api/equipment

# Add new equipment
curl -X POST http://localhost:3000/api/equipment \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Test Camera",
    "type": "camera",
    "manufacturer": "ZWO",
    "model": "ASI2600MC Pro",
    "connectionType": "usb"
  }'

# Connect equipment
curl -X POST http://localhost:3000/api/equipment/eq_001/connect \
  -H "Content-Type: application/json" \
  -d '{"timeout": 5000, "retries": 3}'
```

## Support

For API support and questions:
- GitHub Issues: [Project Repository](https://github.com/your-username/cobalt-mobile)
- Documentation: [API Docs](https://docs.cobalt-mobile.com)
- Community: [Discord Server](https://discord.gg/cobalt-mobile)
