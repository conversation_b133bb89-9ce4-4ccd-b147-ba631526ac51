# Contributing to Cobalt Mobile

Thank you for your interest in contributing to Cobalt Mobile! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites

- Node.js 18+ 
- pnpm (recommended) or npm/yarn
- Git
- Basic knowledge of TypeScript, React, and Next.js

### Development Setup

1. **Fork and Clone**
   ```bash
   git clone https://github.com/your-username/cobalt-mobile.git
   cd cobalt-mobile
   ```

2. **Install Dependencies**
   ```bash
   pnpm install
   ```

3. **Start Development Server**
   ```bash
   pnpm dev
   ```

4. **Run Tests**
   ```bash
   pnpm test
   ```

## 📋 Development Guidelines

### Code Style

- **TypeScript**: Use strict TypeScript with proper type definitions
- **Components**: Use functional components with hooks
- **Styling**: Use Tailwind CSS with the existing design system
- **State Management**: Use Zustand stores for global state
- **Testing**: Write tests for new features and bug fixes

### File Organization

```
src/
├── app/                    # Next.js app directory
├── components/            # React components
│   ├── ui/               # Reusable UI components
│   ├── [feature]/        # Feature-specific components
│   └── __tests__/        # Component tests
├── hooks/                # Custom React hooks
├── lib/                  # Utilities and services
│   ├── stores/           # Zustand stores
│   └── utils/            # Utility functions
└── types/                # TypeScript type definitions
```

### Naming Conventions

- **Files**: kebab-case (`equipment-profiles.tsx`)
- **Components**: PascalCase (`EquipmentProfiles`)
- **Functions**: camelCase (`handleSubmit`)
- **Constants**: UPPER_SNAKE_CASE (`MAX_RETRY_ATTEMPTS`)
- **Types/Interfaces**: PascalCase (`EquipmentProfile`)

## 🔧 Development Process

### Branch Naming

- `feature/description` - New features
- `fix/description` - Bug fixes
- `docs/description` - Documentation updates
- `refactor/description` - Code refactoring
- `test/description` - Test improvements

### Commit Messages

Follow conventional commits format:

```
type(scope): description

[optional body]

[optional footer]
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

Examples:
- `feat(equipment): add filter wheel control`
- `fix(navigation): resolve mobile swipe gesture issue`
- `docs(readme): update installation instructions`

### Pull Request Process

1. **Create Feature Branch**
   ```bash
   git checkout -b feature/your-feature-name
   ```

2. **Make Changes**
   - Write clean, documented code
   - Add tests for new functionality
   - Update documentation if needed

3. **Test Your Changes**
   ```bash
   pnpm test
   pnpm lint
   pnpm type-check
   ```

4. **Commit and Push**
   ```bash
   git add .
   git commit -m "feat(scope): description"
   git push origin feature/your-feature-name
   ```

5. **Create Pull Request**
   - Use the PR template
   - Provide clear description
   - Link related issues
   - Request review

## 🧪 Testing

### Test Types

- **Unit Tests**: Test individual functions and components
- **Integration Tests**: Test component interactions
- **E2E Tests**: Test complete user workflows

### Writing Tests

```typescript
// Component test example
import { render, screen } from '@testing-library/react';
import { EquipmentProfiles } from '../equipment-profiles';

describe('EquipmentProfiles', () => {
  it('should render equipment list', () => {
    render(<EquipmentProfiles />);
    expect(screen.getByText('Equipment Profiles')).toBeInTheDocument();
  });
});
```

### Test Commands

```bash
pnpm test              # Run all tests
pnpm test:watch        # Run tests in watch mode
pnpm test:coverage     # Run tests with coverage
```

## 📝 Documentation

### Code Documentation

- Add JSDoc comments for public APIs
- Document complex algorithms
- Include usage examples
- Keep README.md updated

### Component Documentation

```typescript
/**
 * Equipment profile management component
 * 
 * @param profiles - Array of equipment profiles
 * @param onSelect - Callback when profile is selected
 * @example
 * <EquipmentProfiles 
 *   profiles={profiles} 
 *   onSelect={handleSelect} 
 * />
 */
export function EquipmentProfiles({ profiles, onSelect }: Props) {
  // Component implementation
}
```

## 🐛 Bug Reports

### Before Reporting

1. Check existing issues
2. Reproduce the bug
3. Test on latest version
4. Gather system information

### Bug Report Template

```markdown
**Bug Description**
Clear description of the bug

**Steps to Reproduce**
1. Go to...
2. Click on...
3. See error

**Expected Behavior**
What should happen

**Actual Behavior**
What actually happens

**Environment**
- OS: [e.g., iOS, Android, Windows]
- Browser: [e.g., Chrome, Safari]
- Version: [e.g., 1.0.0]
```

## 💡 Feature Requests

### Feature Request Template

```markdown
**Feature Description**
Clear description of the feature

**Use Case**
Why is this feature needed?

**Proposed Solution**
How should it work?

**Alternatives**
Other solutions considered
```

## 🎯 Areas for Contribution

### High Priority

- Equipment driver implementations
- Mobile gesture improvements
- Performance optimizations
- Accessibility enhancements
- Test coverage improvements

### Medium Priority

- UI/UX improvements
- Documentation updates
- Code refactoring
- New equipment support

### Good First Issues

Look for issues labeled `good-first-issue` for beginner-friendly tasks.

## 📞 Getting Help

- **GitHub Issues**: For bugs and feature requests
- **Discussions**: For questions and general discussion
- **Discord**: [Community chat] (if available)

## 🏆 Recognition

Contributors will be recognized in:
- README.md contributors section
- Release notes
- Project documentation

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to Cobalt Mobile! 🌟
