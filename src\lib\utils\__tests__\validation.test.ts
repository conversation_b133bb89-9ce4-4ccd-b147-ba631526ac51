/**
 * @jest-environment jsdom
 */

import {
  validateValue,
  validateObject,
  isValidEmail,
  isValidUrl,
  isValidIP,
  isValidCoordinate,
  validateEquipmentSettings,
  validateCameraSettings,
  validateMountSettings,
  handleValidationError,
  validateAsync,
  type ValidationRule,
  type ValidationSchema,
} from '../validation';

describe('Validation Utilities', () => {
  describe('validateValue', () => {
    it('should validate required fields', () => {
      const rule: ValidationRule = { required: true };
      
      const validResult = validateValue('test', rule, 'testField');
      expect(validResult.isValid).toBe(true);
      expect(validResult.errors).toHaveLength(0);
      
      const invalidResult = validateValue('', rule, 'testField');
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors).toHaveLength(1);
      expect(invalidResult.errors[0].code).toBe('REQUIRED');
    });

    it('should validate string length', () => {
      const rule: ValidationRule = { minLength: 3, maxLength: 10 };
      
      const validResult = validateValue('hello', rule, 'testField');
      expect(validResult.isValid).toBe(true);
      
      const tooShortResult = validateValue('hi', rule, 'testField');
      expect(tooShortResult.isValid).toBe(false);
      expect(tooShortResult.errors[0].code).toBe('MIN_LENGTH');
      
      const tooLongResult = validateValue('this is too long', rule, 'testField');
      expect(tooLongResult.isValid).toBe(false);
      expect(tooLongResult.errors[0].code).toBe('MAX_LENGTH');
    });

    it('should validate number ranges', () => {
      const rule: ValidationRule = { type: 'number', min: 0, max: 100 };
      
      const validResult = validateValue(50, rule, 'testField');
      expect(validResult.isValid).toBe(true);
      
      const tooSmallResult = validateValue(-10, rule, 'testField');
      expect(tooSmallResult.isValid).toBe(false);
      expect(tooSmallResult.errors[0].code).toBe('MIN_VALUE');
      
      const tooLargeResult = validateValue(150, rule, 'testField');
      expect(tooLargeResult.isValid).toBe(false);
      expect(tooLargeResult.errors[0].code).toBe('MAX_VALUE');
    });

    it('should validate patterns', () => {
      const rule: ValidationRule = { pattern: /^[A-Z]{2,3}$/ };
      
      const validResult = validateValue('ABC', rule, 'testField');
      expect(validResult.isValid).toBe(true);
      
      const invalidResult = validateValue('abc', rule, 'testField');
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors[0].code).toBe('PATTERN');
    });

    it('should validate custom rules', () => {
      const rule: ValidationRule = {
        custom: (value: string) => value.includes('test') || 'Must contain "test"'
      };
      
      const validResult = validateValue('testing', rule, 'testField');
      expect(validResult.isValid).toBe(true);
      
      const invalidResult = validateValue('hello', rule, 'testField');
      expect(invalidResult.isValid).toBe(false);
      expect(invalidResult.errors[0].message).toBe('Must contain "test"');
    });

    it('should validate array length', () => {
      const rule: ValidationRule = { type: 'array', minLength: 1, maxLength: 3 };
      
      const validResult = validateValue(['a', 'b'], rule, 'testField');
      expect(validResult.isValid).toBe(true);
      
      const emptyResult = validateValue([], rule, 'testField');
      expect(emptyResult.isValid).toBe(false);
      expect(emptyResult.errors[0].code).toBe('MIN_ITEMS');
      
      const tooManyResult = validateValue(['a', 'b', 'c', 'd'], rule, 'testField');
      expect(tooManyResult.isValid).toBe(false);
      expect(tooManyResult.errors[0].code).toBe('MAX_ITEMS');
    });
  });

  describe('validateObject', () => {
    it('should validate object against schema', () => {
      const schema: ValidationSchema<any> = {
        name: { required: true, type: 'string', minLength: 1 },
        age: { required: true, type: 'number', min: 0, max: 150 },
        email: { type: 'email' },
      };
      
      const validObject = {
        name: 'John Doe',
        age: 30,
        email: '<EMAIL>',
      };
      
      const result = validateObject(validObject, schema);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should return errors for invalid object', () => {
      const schema: ValidationSchema<any> = {
        name: { required: true, type: 'string', minLength: 1 },
        age: { required: true, type: 'number', min: 0, max: 150 },
      };
      
      const invalidObject = {
        name: '',
        age: -5,
      };
      
      const result = validateObject(invalidObject, schema);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(2);
      expect(result.errors.some(e => e.field === 'name')).toBe(true);
      expect(result.errors.some(e => e.field === 'age')).toBe(true);
    });
  });

  describe('Type validation functions', () => {
    describe('isValidEmail', () => {
      it('should validate correct email addresses', () => {
        expect(isValidEmail('<EMAIL>')).toBe(true);
        expect(isValidEmail('<EMAIL>')).toBe(true);
        expect(isValidEmail('<EMAIL>')).toBe(true);
      });

      it('should reject invalid email addresses', () => {
        expect(isValidEmail('invalid-email')).toBe(false);
        expect(isValidEmail('@example.com')).toBe(false);
        expect(isValidEmail('test@')).toBe(false);
        expect(isValidEmail('test.example.com')).toBe(false);
      });
    });

    describe('isValidUrl', () => {
      it('should validate correct URLs', () => {
        expect(isValidUrl('https://example.com')).toBe(true);
        expect(isValidUrl('http://localhost:3000')).toBe(true);
        expect(isValidUrl('ftp://files.example.com')).toBe(true);
      });

      it('should reject invalid URLs', () => {
        expect(isValidUrl('not-a-url')).toBe(false);
        expect(isValidUrl('http://')).toBe(false);
        expect(isValidUrl('://example.com')).toBe(false);
      });
    });

    describe('isValidIP', () => {
      it('should validate correct IPv4 addresses', () => {
        expect(isValidIP('***********')).toBe(true);
        expect(isValidIP('********')).toBe(true);
        expect(isValidIP('***************')).toBe(true);
        expect(isValidIP('0.0.0.0')).toBe(true);
      });

      it('should reject invalid IPv4 addresses', () => {
        expect(isValidIP('256.1.1.1')).toBe(false);
        expect(isValidIP('192.168.1')).toBe(false);
        expect(isValidIP('***********.1')).toBe(false);
        expect(isValidIP('not-an-ip')).toBe(false);
      });
    });

    describe('isValidCoordinate', () => {
      it('should validate correct RA coordinates', () => {
        expect(isValidCoordinate('12:30:45')).toBe(true);
        expect(isValidCoordinate('00:00:00')).toBe(true);
        expect(isValidCoordinate('23:59:59')).toBe(true);
        expect(isValidCoordinate('12 30 45')).toBe(true);
      });

      it('should validate correct Dec coordinates', () => {
        expect(isValidCoordinate('+45:30:15')).toBe(true);
        expect(isValidCoordinate('-30:45:30')).toBe(true);
        expect(isValidCoordinate('90:00:00')).toBe(true);
        expect(isValidCoordinate('+45 30 15')).toBe(true);
      });

      it('should reject invalid coordinates', () => {
        expect(isValidCoordinate('25:00:00')).toBe(false); // Invalid hour
        expect(isValidCoordinate('12:60:00')).toBe(false); // Invalid minute
        expect(isValidCoordinate('12:30:60')).toBe(false); // Invalid second
        expect(isValidCoordinate('91:00:00')).toBe(false); // Invalid declination
        expect(isValidCoordinate('not-a-coordinate')).toBe(false);
      });
    });
  });

  describe('Equipment validation functions', () => {
    describe('validateEquipmentSettings', () => {
      it('should validate correct equipment settings', () => {
        const settings = {
          host: '*************',
          port: 8080,
          timeout: 5000,
          retries: 3,
        };
        
        const result = validateEquipmentSettings(settings);
        expect(result.isValid).toBe(true);
      });

      it('should reject invalid equipment settings', () => {
        const settings = {
          host: '',
          port: 70000, // Invalid port
          timeout: 100, // Too short
          retries: -1,  // Invalid
        };
        
        const result = validateEquipmentSettings(settings);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    describe('validateCameraSettings', () => {
      it('should validate correct camera settings', () => {
        const settings = {
          exposureTime: 60,
          gain: 50,
          binning: 2,
          temperature: -10,
        };
        
        const result = validateCameraSettings(settings);
        expect(result.isValid).toBe(true);
      });

      it('should reject invalid camera settings', () => {
        const settings = {
          exposureTime: -1,   // Invalid
          gain: 150,          // Too high
          binning: 0,         // Too low
          temperature: 100,   // Too high
        };
        
        const result = validateCameraSettings(settings);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });

    describe('validateMountSettings', () => {
      it('should validate correct mount settings', () => {
        const settings = {
          ra: '12:30:45',
          dec: '+45:30:15',
          slewRate: 'max',
          trackingRate: 'sidereal',
        };
        
        const result = validateMountSettings(settings);
        expect(result.isValid).toBe(true);
      });

      it('should reject invalid mount settings', () => {
        const settings = {
          ra: 'invalid-ra',
          dec: 'invalid-dec',
          slewRate: 'invalid-rate',
          trackingRate: 'invalid-tracking',
        };
        
        const result = validateMountSettings(settings);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThan(0);
      });
    });
  });

  describe('handleValidationError', () => {
    it('should throw error for invalid validation result', () => {
      const result = {
        isValid: false,
        errors: [
          { field: 'name', message: 'Name is required', code: 'REQUIRED', value: '' }
        ],
        warnings: [],
      };
      
      expect(() => handleValidationError(result)).toThrow('Validation failed');
    });

    it('should not throw error for valid validation result', () => {
      const result = {
        isValid: true,
        errors: [],
        warnings: [],
      };
      
      expect(() => handleValidationError(result)).not.toThrow();
    });
  });

  describe('validateAsync', () => {
    it('should handle successful async validation', async () => {
      const validator = jest.fn().mockResolvedValue(true);
      const result = await validateAsync('test-value', validator);
      
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
      expect(validator).toHaveBeenCalledWith('test-value');
    });

    it('should handle failed async validation with string message', async () => {
      const validator = jest.fn().mockResolvedValue('Validation failed');
      const result = await validateAsync('test-value', validator);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('Validation failed');
    });

    it('should handle async validation errors', async () => {
      const validator = jest.fn().mockRejectedValue(new Error('Async error'));
      const result = await validateAsync('test-value', validator);
      
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(1);
      expect(result.errors[0].message).toBe('Async error');
      expect(result.errors[0].code).toBe('ASYNC_ERROR');
    });
  });

  describe('Complex validation scenarios', () => {
    it('should handle nested object validation', () => {
      const schema: ValidationSchema<any> = {
        user: { required: true, type: 'object' },
        settings: { type: 'object' },
      };
      
      const validObject = {
        user: { name: 'John', email: '<EMAIL>' },
        settings: { theme: 'dark', notifications: true },
      };
      
      const result = validateObject(validObject, schema);
      expect(result.isValid).toBe(true);
    });

    it('should handle multiple validation errors', () => {
      const schema: ValidationSchema<any> = {
        name: { required: true, minLength: 3 },
        email: { required: true, type: 'email' },
        age: { required: true, type: 'number', min: 0 },
      };
      
      const invalidObject = {
        name: 'Jo',              // Too short
        email: 'invalid-email',  // Invalid format
        age: -5,                 // Below minimum
      };
      
      const result = validateObject(invalidObject, schema);
      expect(result.isValid).toBe(false);
      expect(result.errors).toHaveLength(3);
    });

    it('should handle optional fields correctly', () => {
      const schema: ValidationSchema<any> = {
        name: { required: true, type: 'string' },
        nickname: { type: 'string', minLength: 2 }, // Optional
      };
      
      const validObject = {
        name: 'John Doe',
        // nickname is optional and not provided
      };
      
      const result = validateObject(validObject, schema);
      expect(result.isValid).toBe(true);
    });
  });
});
