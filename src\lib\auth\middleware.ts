import { NextRequest, NextResponse } from 'next/server';
import { JWTUtils, PermissionUtils, SecurityUtils } from './auth-utils';

// Rate limiting store (in production, use Redis)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Rate limiting configuration
const RATE_LIMITS = {
  login: { maxAttempts: 5, windowMs: 15 * 60 * 1000 }, // 5 attempts per 15 minutes
  register: { maxAttempts: 3, windowMs: 60 * 60 * 1000 }, // 3 attempts per hour
  passwordReset: { maxAttempts: 3, windowMs: 60 * 60 * 1000 }, // 3 attempts per hour
  default: { maxAttempts: 100, windowMs: 15 * 60 * 1000 }, // 100 requests per 15 minutes
} as const;

// Middleware types
export interface AuthMiddlewareOptions {
  requiredPermissions?: string[];
  requiredRole?: string;
  allowUnverifiedEmail?: boolean;
}

export interface RateLimitOptions {
  action: keyof typeof RATE_LIMITS;
  skipSuccessfulRequests?: boolean;
}

// Rate limiting middleware
export function rateLimit(options: RateLimitOptions) {
  return (request: NextRequest): NextResponse | null => {
    const ip = request.ip || request.headers.get('x-forwarded-for') || 'unknown';
    const key = SecurityUtils.generateRateLimitKey(ip, options.action);
    const limit = RATE_LIMITS[options.action];
    
    const now = Date.now();
    const record = rateLimitStore.get(key);
    
    if (!record || now > record.resetTime) {
      // Reset or create new record
      rateLimitStore.set(key, { count: 1, resetTime: now + limit.windowMs });
      return null; // Allow request
    }
    
    if (record.count >= limit.maxAttempts) {
      return NextResponse.json(
        {
          success: false,
          error: 'Too many requests',
          code: 'RATE_LIMIT_EXCEEDED',
          retryAfter: Math.ceil((record.resetTime - now) / 1000),
        },
        { status: 429 }
      );
    }
    
    // Increment counter
    record.count++;
    rateLimitStore.set(key, record);
    
    return null; // Allow request
  };
}

// Authentication middleware
export function requireAuth(options: AuthMiddlewareOptions = {}) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    try {
      const user = await JWTUtils.getUserFromRequest(request);
      
      if (!user) {
        return NextResponse.json(
          {
            success: false,
            error: 'Authentication required',
            code: 'UNAUTHORIZED',
          },
          { status: 401 }
        );
      }
      
      // Check email verification if required
      if (!options.allowUnverifiedEmail && !user.emailVerified) {
        return NextResponse.json(
          {
            success: false,
            error: 'Email verification required',
            code: 'EMAIL_NOT_VERIFIED',
          },
          { status: 403 }
        );
      }
      
      // Check role if required
      if (options.requiredRole && !PermissionUtils.hasRole(user.role, options.requiredRole)) {
        return NextResponse.json(
          {
            success: false,
            error: 'Insufficient privileges',
            code: 'FORBIDDEN',
          },
          { status: 403 }
        );
      }
      
      // Check permissions if required
      if (options.requiredPermissions && options.requiredPermissions.length > 0) {
        const hasPermission = PermissionUtils.hasAllPermissions(
          user.permissions,
          options.requiredPermissions
        );
        
        if (!hasPermission) {
          return NextResponse.json(
            {
              success: false,
              error: 'Insufficient permissions',
              code: 'FORBIDDEN',
              details: {
                required: options.requiredPermissions,
                current: user.permissions,
              },
            },
            { status: 403 }
          );
        }
      }
      
      // Add user to request headers for downstream handlers
      const requestHeaders = new Headers(request.headers);
      requestHeaders.set('x-user-id', user.userId);
      requestHeaders.set('x-user-email', user.email);
      requestHeaders.set('x-user-role', user.role);
      requestHeaders.set('x-user-permissions', JSON.stringify(user.permissions));
      
      return null; // Allow request to continue
      
    } catch (error) {
      console.error('Auth middleware error:', error);
      return NextResponse.json(
        {
          success: false,
          error: 'Authentication error',
          code: 'AUTH_ERROR',
        },
        { status: 500 }
      );
    }
  };
}

// Input validation middleware
export function validateInput(schema: Record<string, any>) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    try {
      const body = await request.json();
      const errors: string[] = [];
      
      // Basic validation
      for (const [field, rules] of Object.entries(schema)) {
        const value = body[field];
        
        if (rules.required && (!value || value.toString().trim() === '')) {
          errors.push(`${field} is required`);
          continue;
        }
        
        if (value && rules.type) {
          switch (rules.type) {
            case 'email':
              if (!SecurityUtils.isValidEmail(value)) {
                errors.push(`${field} must be a valid email address`);
              }
              break;
            case 'username':
              if (!SecurityUtils.isValidUsername(value)) {
                errors.push(`${field} must be a valid username (3-30 characters, alphanumeric, underscore, hyphen)`);
              }
              break;
            case 'string':
              if (typeof value !== 'string') {
                errors.push(`${field} must be a string`);
              } else {
                if (rules.minLength && value.length < rules.minLength) {
                  errors.push(`${field} must be at least ${rules.minLength} characters long`);
                }
                if (rules.maxLength && value.length > rules.maxLength) {
                  errors.push(`${field} must be no more than ${rules.maxLength} characters long`);
                }
              }
              break;
          }
        }
        
        // Sanitize input
        if (typeof value === 'string') {
          body[field] = SecurityUtils.sanitizeInput(value);
        }
      }
      
      if (errors.length > 0) {
        return NextResponse.json(
          {
            success: false,
            error: 'Validation failed',
            code: 'VALIDATION_ERROR',
            details: errors,
          },
          { status: 400 }
        );
      }
      
      return null; // Allow request to continue
      
    } catch (error) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid JSON',
          code: 'INVALID_JSON',
        },
        { status: 400 }
      );
    }
  };
}

// CSRF protection middleware
export function csrfProtection() {
  return (request: NextRequest): NextResponse | null => {
    // Skip CSRF for GET requests
    if (request.method === 'GET') {
      return null;
    }
    
    const csrfToken = request.headers.get('x-csrf-token');
    const sessionCsrfToken = request.headers.get('x-session-csrf-token');
    
    if (!csrfToken || !sessionCsrfToken || csrfToken !== sessionCsrfToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'CSRF token mismatch',
          code: 'CSRF_ERROR',
        },
        { status: 403 }
      );
    }
    
    return null;
  };
}

// Combine multiple middleware functions
export function combineMiddleware(...middlewares: Array<(request: NextRequest) => Promise<NextResponse | null> | NextResponse | null>) {
  return async (request: NextRequest): Promise<NextResponse | null> => {
    for (const middleware of middlewares) {
      const result = await middleware(request);
      if (result) {
        return result; // Stop on first middleware that returns a response
      }
    }
    return null;
  };
}

// Helper to get user from request headers (after auth middleware)
export function getUserFromHeaders(request: NextRequest) {
  const userId = request.headers.get('x-user-id');
  const email = request.headers.get('x-user-email');
  const role = request.headers.get('x-user-role');
  const permissions = request.headers.get('x-user-permissions');
  
  if (!userId || !email || !role) {
    return null;
  }
  
  return {
    userId,
    email,
    role,
    permissions: permissions ? JSON.parse(permissions) : [],
  };
}
