/**
 * Enhanced Performance Monitoring System
 * Provides comprehensive performance tracking and optimization insights
 */

import { logger } from './logging';

// Performance metric types
export interface PerformanceMetric {
  id: string;
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  category: PerformanceCategory;
  context?: Record<string, unknown>;
}

export type PerformanceCategory = 
  | 'render'
  | 'network'
  | 'memory'
  | 'interaction'
  | 'navigation'
  | 'bundle'
  | 'custom';

// Performance thresholds
export interface PerformanceThresholds {
  render: {
    componentRender: number; // ms
    pageLoad: number; // ms
    firstContentfulPaint: number; // ms
  };
  network: {
    apiResponse: number; // ms
    resourceLoad: number; // ms
  };
  memory: {
    heapUsed: number; // MB
    heapTotal: number; // MB
  };
  interaction: {
    clickResponse: number; // ms
    inputDelay: number; // ms
  };
}

const DEFAULT_THRESHOLDS: PerformanceThresholds = {
  render: {
    componentRender: 16, // 60fps
    pageLoad: 3000,
    firstContentfulPaint: 1500,
  },
  network: {
    apiResponse: 2000,
    resourceLoad: 5000,
  },
  memory: {
    heapUsed: 100,
    heapTotal: 200,
  },
  interaction: {
    clickResponse: 100,
    inputDelay: 50,
  },
};

class PerformanceMonitor {
  private metrics: PerformanceMetric[] = [];
  private thresholds: PerformanceThresholds;
  private observers: Map<string, PerformanceObserver> = new Map();
  private isEnabled: boolean;

  constructor(thresholds: Partial<PerformanceThresholds> = {}) {
    this.thresholds = { ...DEFAULT_THRESHOLDS, ...thresholds };
    this.isEnabled = process.env.NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING === 'true';
    
    if (this.isEnabled && typeof window !== 'undefined') {
      this.initializeObservers();
    }
  }

  // Initialize performance observers
  private initializeObservers(): void {
    // Navigation timing
    if ('PerformanceObserver' in window) {
      try {
        const navObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              this.recordNavigationMetrics(entry as PerformanceNavigationTiming);
            }
          }
        });
        navObserver.observe({ entryTypes: ['navigation'] });
        this.observers.set('navigation', navObserver);
      } catch (error) {
        logger.warn('Failed to initialize navigation observer', { error });
      }

      // Paint timing
      try {
        const paintObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric({
              name: entry.name,
              value: entry.startTime,
              unit: 'ms',
              category: 'render',
            });
          }
        });
        paintObserver.observe({ entryTypes: ['paint'] });
        this.observers.set('paint', paintObserver);
      } catch (error) {
        logger.warn('Failed to initialize paint observer', { error });
      }

      // Largest Contentful Paint
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1];
          this.recordMetric({
            name: 'largest-contentful-paint',
            value: lastEntry.startTime,
            unit: 'ms',
            category: 'render',
          });
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        this.observers.set('lcp', lcpObserver);
      } catch (error) {
        logger.warn('Failed to initialize LCP observer', { error });
      }

      // First Input Delay
      try {
        const fidObserver = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            this.recordMetric({
              name: 'first-input-delay',
              value: (entry as any).processingStart - entry.startTime,
              unit: 'ms',
              category: 'interaction',
            });
          }
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        this.observers.set('fid', fidObserver);
      } catch (error) {
        logger.warn('Failed to initialize FID observer', { error });
      }
    }
  }

  // Record navigation metrics
  private recordNavigationMetrics(entry: PerformanceNavigationTiming): void {
    const metrics = [
      { name: 'dns-lookup', value: entry.domainLookupEnd - entry.domainLookupStart },
      { name: 'tcp-connect', value: entry.connectEnd - entry.connectStart },
      { name: 'request-response', value: entry.responseEnd - entry.requestStart },
      { name: 'dom-processing', value: entry.domComplete - entry.domLoading },
      { name: 'page-load', value: entry.loadEventEnd - entry.navigationStart },
    ];

    metrics.forEach(metric => {
      this.recordMetric({
        name: metric.name,
        value: metric.value,
        unit: 'ms',
        category: 'navigation',
      });
    });
  }

  // Record a performance metric
  recordMetric(metric: Omit<PerformanceMetric, 'id' | 'timestamp'>): void {
    if (!this.isEnabled) return;

    const fullMetric: PerformanceMetric = {
      ...metric,
      id: this.generateId(),
      timestamp: new Date(),
    };

    this.metrics.push(fullMetric);

    // Check thresholds and log warnings
    this.checkThresholds(fullMetric);

    // Log performance metric
    logger.performance(
      `Performance metric: ${metric.name}`,
      metric.value,
      { category: metric.category, unit: metric.unit, ...metric.context }
    );

    // Maintain metrics array size
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500);
    }
  }

  // Check performance thresholds
  private checkThresholds(metric: PerformanceMetric): void {
    let threshold: number | undefined;

    switch (metric.category) {
      case 'render':
        if (metric.name === 'component-render') {
          threshold = this.thresholds.render.componentRender;
        } else if (metric.name === 'page-load') {
          threshold = this.thresholds.render.pageLoad;
        } else if (metric.name === 'first-contentful-paint') {
          threshold = this.thresholds.render.firstContentfulPaint;
        }
        break;
      case 'network':
        if (metric.name.includes('api')) {
          threshold = this.thresholds.network.apiResponse;
        } else {
          threshold = this.thresholds.network.resourceLoad;
        }
        break;
      case 'interaction':
        if (metric.name === 'click-response') {
          threshold = this.thresholds.interaction.clickResponse;
        } else if (metric.name === 'first-input-delay') {
          threshold = this.thresholds.interaction.inputDelay;
        }
        break;
    }

    if (threshold && metric.value > threshold) {
      logger.warn(`Performance threshold exceeded: ${metric.name}`, {
        value: metric.value,
        threshold,
        unit: metric.unit,
        category: metric.category,
      });
    }
  }

  // Measure function execution time
  async measureAsync<T>(
    name: string,
    fn: () => Promise<T>,
    category: PerformanceCategory = 'custom',
    context?: Record<string, unknown>
  ): Promise<T> {
    const startTime = performance.now();
    
    try {
      const result = await fn();
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name,
        value: duration,
        unit: 'ms',
        category,
        context: { ...context, success: true },
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name,
        value: duration,
        unit: 'ms',
        category,
        context: { 
          ...context, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        },
      });
      
      throw error;
    }
  }

  // Measure synchronous function execution time
  measure<T>(
    name: string,
    fn: () => T,
    category: PerformanceCategory = 'custom',
    context?: Record<string, unknown>
  ): T {
    const startTime = performance.now();
    
    try {
      const result = fn();
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name,
        value: duration,
        unit: 'ms',
        category,
        context: { ...context, success: true },
      });
      
      return result;
    } catch (error) {
      const duration = performance.now() - startTime;
      
      this.recordMetric({
        name,
        value: duration,
        unit: 'ms',
        category,
        context: { 
          ...context, 
          success: false, 
          error: error instanceof Error ? error.message : 'Unknown error' 
        },
      });
      
      throw error;
    }
  }

  // Get memory usage
  getMemoryUsage(): { used: number; total: number; limit?: number } | null {
    if (typeof window !== 'undefined' && 'memory' in performance) {
      const memory = (performance as any).memory;
      return {
        used: Math.round(memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(memory.totalJSHeapSize / 1024 / 1024),
        limit: memory.jsHeapSizeLimit ? Math.round(memory.jsHeapSizeLimit / 1024 / 1024) : undefined,
      };
    }
    return null;
  }

  // Record memory usage
  recordMemoryUsage(): void {
    const memory = this.getMemoryUsage();
    if (memory) {
      this.recordMetric({
        name: 'memory-used',
        value: memory.used,
        unit: 'MB',
        category: 'memory',
        context: { total: memory.total, limit: memory.limit },
      });
    }
  }

  // Get performance summary
  getSummary(timeRange?: { start: Date; end: Date }): {
    total: number;
    byCategory: Record<PerformanceCategory, number>;
    averages: Record<string, number>;
    slowest: PerformanceMetric[];
    thresholdViolations: number;
  } {
    let filteredMetrics = this.metrics;

    if (timeRange) {
      filteredMetrics = this.metrics.filter(
        metric => metric.timestamp >= timeRange.start && metric.timestamp <= timeRange.end
      );
    }

    const summary = {
      total: filteredMetrics.length,
      byCategory: {} as Record<PerformanceCategory, number>,
      averages: {} as Record<string, number>,
      slowest: [] as PerformanceMetric[],
      thresholdViolations: 0,
    };

    // Count by category
    filteredMetrics.forEach(metric => {
      summary.byCategory[metric.category] = (summary.byCategory[metric.category] || 0) + 1;
    });

    // Calculate averages
    const metricGroups = new Map<string, number[]>();
    filteredMetrics.forEach(metric => {
      if (!metricGroups.has(metric.name)) {
        metricGroups.set(metric.name, []);
      }
      metricGroups.get(metric.name)!.push(metric.value);
    });

    metricGroups.forEach((values, name) => {
      summary.averages[name] = values.reduce((sum, val) => sum + val, 0) / values.length;
    });

    // Find slowest operations
    summary.slowest = filteredMetrics
      .sort((a, b) => b.value - a.value)
      .slice(0, 10);

    return summary;
  }

  // Clear metrics
  clearMetrics(): void {
    this.metrics = [];
  }

  // Export metrics
  exportMetrics(): string {
    return JSON.stringify(this.metrics, null, 2);
  }

  // Cleanup observers
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect());
    this.observers.clear();
  }

  private generateId(): string {
    return `perf-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Create singleton instance
export const performanceMonitor = new PerformanceMonitor();

// React hook for component performance monitoring
export function usePerformanceMonitor(componentName: string) {
  const measureRender = React.useCallback((renderTime: number) => {
    performanceMonitor.recordMetric({
      name: 'component-render',
      value: renderTime,
      unit: 'ms',
      category: 'render',
      context: { component: componentName },
    });
  }, [componentName]);

  const measureInteraction = React.useCallback((interactionName: string, duration: number) => {
    performanceMonitor.recordMetric({
      name: interactionName,
      value: duration,
      unit: 'ms',
      category: 'interaction',
      context: { component: componentName },
    });
  }, [componentName]);

  return { measureRender, measureInteraction };
}

export default performanceMonitor;
