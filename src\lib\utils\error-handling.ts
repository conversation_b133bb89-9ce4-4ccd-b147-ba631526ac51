/**
 * Enhanced Error Handling Utilities
 * Provides comprehensive error handling, logging, and recovery mechanisms
 */

import { createNotification } from '@/lib/notifications';

// Error severity levels
export type ErrorSeverity = 'low' | 'medium' | 'high' | 'critical';

// Error categories for better organization
export type ErrorCategory = 
  | 'network'
  | 'validation'
  | 'equipment'
  | 'user'
  | 'system'
  | 'security'
  | 'performance';

// Enhanced error interface
export interface EnhancedError extends Error {
  code?: string;
  severity?: ErrorSeverity;
  category?: ErrorCategory;
  context?: Record<string, unknown>;
  timestamp?: Date;
  userId?: string;
  sessionId?: string;
  retryable?: boolean;
  suggestions?: string[];
}

// Error recovery strategies
export type RecoveryStrategy = 
  | 'retry'
  | 'fallback'
  | 'ignore'
  | 'escalate'
  | 'user-action';

export interface RecoveryOptions {
  strategy: RecoveryStrategy;
  maxRetries?: number;
  retryDelay?: number;
  fallbackValue?: unknown;
  userMessage?: string;
  onRecovery?: () => void;
}

// Create enhanced error
export function createError(
  message: string,
  options: Partial<EnhancedError> = {}
): EnhancedError {
  const error = new Error(message) as EnhancedError;
  
  error.code = options.code || 'UNKNOWN_ERROR';
  error.severity = options.severity || 'medium';
  error.category = options.category || 'system';
  error.context = options.context || {};
  error.timestamp = new Date();
  error.retryable = options.retryable ?? true;
  error.suggestions = options.suggestions || [];
  
  // Add session context if available
  if (typeof window !== 'undefined') {
    error.sessionId = sessionStorage.getItem('sessionId') || 'unknown';
  }
  
  return error;
}

// Error handler with recovery
export async function handleError<T>(
  error: Error | EnhancedError,
  recovery?: RecoveryOptions
): Promise<T | null> {
  const enhancedError = error as EnhancedError;
  
  // Log error
  logError(enhancedError);
  
  // Show user notification based on severity
  showErrorNotification(enhancedError);
  
  // Attempt recovery if specified
  if (recovery) {
    return attemptRecovery<T>(enhancedError, recovery);
  }
  
  return null;
}

// Log error with context
export function logError(error: EnhancedError): void {
  const logData = {
    message: error.message,
    code: error.code,
    severity: error.severity,
    category: error.category,
    context: error.context,
    timestamp: error.timestamp,
    stack: error.stack,
    userAgent: typeof navigator !== 'undefined' ? navigator.userAgent : 'unknown',
    url: typeof window !== 'undefined' ? window.location.href : 'unknown',
  };
  
  // Console logging with appropriate level
  switch (error.severity) {
    case 'critical':
      console.error('CRITICAL ERROR:', logData);
      break;
    case 'high':
      console.error('HIGH SEVERITY ERROR:', logData);
      break;
    case 'medium':
      console.warn('MEDIUM SEVERITY ERROR:', logData);
      break;
    case 'low':
      console.info('LOW SEVERITY ERROR:', logData);
      break;
    default:
      console.log('ERROR:', logData);
  }
  
  // Send to external logging service in production
  if (process.env.NODE_ENV === 'production') {
    sendToLoggingService(logData);
  }
}

// Show user notification based on error severity
function showErrorNotification(error: EnhancedError): void {
  const shouldShowToUser = error.severity === 'high' || error.severity === 'critical';
  
  if (shouldShowToUser) {
    const title = getErrorTitle(error);
    const message = getUserFriendlyMessage(error);
    
    createNotification.error(title, message, {
      category: error.category || 'system',
      persistent: error.severity === 'critical',
      actions: error.suggestions?.map(suggestion => ({
        label: suggestion,
        action: () => console.log(`User selected: ${suggestion}`)
      }))
    });
  }
}

// Get user-friendly error title
function getErrorTitle(error: EnhancedError): string {
  switch (error.category) {
    case 'network':
      return 'Connection Problem';
    case 'equipment':
      return 'Equipment Error';
    case 'validation':
      return 'Invalid Input';
    case 'security':
      return 'Security Issue';
    case 'performance':
      return 'Performance Issue';
    default:
      return 'Application Error';
  }
}

// Get user-friendly error message
function getUserFriendlyMessage(error: EnhancedError): string {
  // Map technical errors to user-friendly messages
  const friendlyMessages: Record<string, string> = {
    'NETWORK_ERROR': 'Unable to connect to the server. Please check your internet connection.',
    'TIMEOUT_ERROR': 'The operation took too long to complete. Please try again.',
    'VALIDATION_ERROR': 'Please check your input and try again.',
    'EQUIPMENT_TIMEOUT': 'Equipment is not responding. Please check connections.',
    'CAMERA_ERROR': 'Camera communication failed. Please check camera connection.',
    'MOUNT_ERROR': 'Mount communication failed. Please check mount connection.',
    'FOCUSER_ERROR': 'Focuser communication failed. Please check focuser connection.',
    'FILTERWHEEL_ERROR': 'Filter wheel communication failed. Please check filter wheel connection.',
  };
  
  return friendlyMessages[error.code || ''] || error.message;
}

// Attempt error recovery
async function attemptRecovery<T>(
  error: EnhancedError,
  recovery: RecoveryOptions
): Promise<T | null> {
  switch (recovery.strategy) {
    case 'retry':
      return attemptRetry<T>(error, recovery);
    case 'fallback':
      return recovery.fallbackValue as T;
    case 'ignore':
      return null;
    case 'user-action':
      if (recovery.userMessage) {
        createNotification.info('Action Required', recovery.userMessage);
      }
      return null;
    case 'escalate':
      // Escalate to higher severity
      const escalatedError = createError(error.message, {
        ...error,
        severity: 'critical',
        context: { ...error.context, escalated: true }
      });
      logError(escalatedError);
      return null;
    default:
      return null;
  }
}

// Retry mechanism with exponential backoff
async function attemptRetry<T>(
  error: EnhancedError,
  recovery: RecoveryOptions
): Promise<T | null> {
  const maxRetries = recovery.maxRetries || 3;
  const baseDelay = recovery.retryDelay || 1000;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // Wait with exponential backoff
      if (attempt > 1) {
        const delay = baseDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));
      }
      
      // Call recovery callback if provided
      if (recovery.onRecovery) {
        recovery.onRecovery();
      }
      
      // Return null to indicate retry should be handled by caller
      return null;
      
    } catch (retryError) {
      if (attempt === maxRetries) {
        // Final attempt failed
        const finalError = createError(`Retry failed after ${maxRetries} attempts`, {
          code: 'RETRY_EXHAUSTED',
          severity: 'high',
          category: error.category,
          context: { originalError: error.message, attempts: maxRetries }
        });
        logError(finalError);
        return null;
      }
    }
  }
  
  return null;
}

// Send error to external logging service
function sendToLoggingService(logData: any): void {
  // Implementation would depend on your logging service
  // Examples: Sentry, LogRocket, DataDog, etc.
  
  try {
    // Example implementation
    if (typeof window !== 'undefined' && (window as any).Sentry) {
      (window as any).Sentry.captureException(new Error(logData.message), {
        tags: {
          severity: logData.severity,
          category: logData.category,
          code: logData.code,
        },
        extra: logData.context,
      });
    }
  } catch (loggingError) {
    console.error('Failed to send error to logging service:', loggingError);
  }
}

// Async error boundary for promises
export function withErrorHandling<T extends (...args: any[]) => Promise<any>>(
  fn: T,
  recovery?: RecoveryOptions
): T {
  return (async (...args: Parameters<T>) => {
    try {
      return await fn(...args);
    } catch (error) {
      return handleError(error as Error, recovery);
    }
  }) as T;
}

// Error boundary hook for React components
export function useErrorBoundary() {
  const [error, setError] = React.useState<EnhancedError | null>(null);
  
  const captureError = React.useCallback((error: Error | EnhancedError) => {
    const enhancedError = error as EnhancedError;
    logError(enhancedError);
    setError(enhancedError);
  }, []);
  
  const resetError = React.useCallback(() => {
    setError(null);
  }, []);
  
  React.useEffect(() => {
    if (error) {
      throw error;
    }
  }, [error]);
  
  return { captureError, resetError, error };
}

// Common error types for the application
export const ErrorCodes = {
  // Network errors
  NETWORK_ERROR: 'NETWORK_ERROR',
  TIMEOUT_ERROR: 'TIMEOUT_ERROR',
  OFFLINE_ERROR: 'OFFLINE_ERROR',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_INPUT: 'INVALID_INPUT',
  MISSING_REQUIRED: 'MISSING_REQUIRED',
  
  // Equipment errors
  EQUIPMENT_TIMEOUT: 'EQUIPMENT_TIMEOUT',
  CAMERA_ERROR: 'CAMERA_ERROR',
  MOUNT_ERROR: 'MOUNT_ERROR',
  FOCUSER_ERROR: 'FOCUSER_ERROR',
  FILTERWHEEL_ERROR: 'FILTERWHEEL_ERROR',
  
  // System errors
  MEMORY_ERROR: 'MEMORY_ERROR',
  STORAGE_ERROR: 'STORAGE_ERROR',
  PERMISSION_ERROR: 'PERMISSION_ERROR',
  
  // User errors
  USER_CANCELLED: 'USER_CANCELLED',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
} as const;
