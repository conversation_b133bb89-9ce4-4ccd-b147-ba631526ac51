/**
 * @jest-environment node
 */

import { NextRequest } from 'next/server';
import { GET, POST, PUT, DELETE } from '../equipment/route';

// Mock the logger
jest.mock('@/lib/utils/logging', () => ({
  logger: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
  },
}));

// Mock the validation utilities
jest.mock('@/lib/utils/validation', () => ({
  validateObject: jest.fn(),
}));

// Mock the error handling utilities
jest.mock('@/lib/utils/error-handling', () => ({
  createError: jest.fn(),
  ErrorCodes: {
    NETWORK_ERROR: 'NETWORK_ERROR',
    VALIDATION_ERROR: 'VALIDATION_ERROR',
  },
}));

// Helper function to create mock NextRequest
function createMockRequest(
  method: string = 'GET',
  url: string = 'http://localhost:3000/api/equipment',
  body?: any
): NextRequest {
  const request = new NextRequest(url, {
    method,
    headers: {
      'Content-Type': 'application/json',
      'User-Agent': 'Test Agent',
    },
    body: body ? JSON.stringify(body) : undefined,
  });
  
  return request;
}

describe('/api/equipment', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('GET /api/equipment', () => {
    it('should return equipment list successfully', async () => {
      const request = createMockRequest('GET');
      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(Array.isArray(data.data)).toBe(true);
      expect(data.message).toBe('Equipment retrieved successfully');
    });

    it('should include equipment status information', async () => {
      const request = createMockRequest('GET');
      const response = await GET(request);
      const data = await response.json();

      expect(data.data.length).toBeGreaterThan(0);
      
      const equipment = data.data[0];
      expect(equipment).toHaveProperty('id');
      expect(equipment).toHaveProperty('name');
      expect(equipment).toHaveProperty('type');
      expect(equipment).toHaveProperty('status');
      expect(equipment).toHaveProperty('connectionHealth');
    });

    it('should filter equipment by user', async () => {
      const request = createMockRequest('GET');
      const response = await GET(request);
      const data = await response.json();

      // All equipment should belong to the test user
      data.data.forEach((equipment: any) => {
        expect(equipment.userId).toBe('user_001');
      });
    });
  });

  describe('POST /api/equipment', () => {
    const { validateObject } = require('@/lib/utils/validation');

    it('should add new equipment successfully', async () => {
      validateObject.mockReturnValue({ isValid: true, errors: [], warnings: [] });

      const newEquipment = {
        name: 'Test Camera',
        type: 'camera',
        manufacturer: 'Test Manufacturer',
        model: 'Test Model',
        serialNumber: 'TEST001',
        connectionType: 'usb',
        capabilities: ['cooling'],
        settings: { gain: 100 },
      };

      const request = createMockRequest('POST', 'http://localhost:3000/api/equipment', newEquipment);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data).toMatchObject({
        name: newEquipment.name,
        type: newEquipment.type,
        manufacturer: newEquipment.manufacturer,
        model: newEquipment.model,
        status: 'disconnected',
        userId: 'user_001',
      });
      expect(data.data).toHaveProperty('id');
      expect(data.data).toHaveProperty('createdAt');
      expect(data.data).toHaveProperty('updatedAt');
    });

    it('should reject invalid equipment data', async () => {
      validateObject.mockReturnValue({
        isValid: false,
        errors: [
          { field: 'name', message: 'Name is required', code: 'REQUIRED' },
          { field: 'type', message: 'Invalid type', code: 'PATTERN' },
        ],
        warnings: [],
      });

      const invalidEquipment = {
        name: '',
        type: 'invalid-type',
      };

      const request = createMockRequest('POST', 'http://localhost:3000/api/equipment', invalidEquipment);
      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Validation failed');
      expect(data.details).toHaveLength(2);
    });

    it('should handle malformed JSON', async () => {
      const request = new NextRequest('http://localhost:3000/api/equipment', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid-json',
      });

      const response = await POST(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
    });
  });

  describe('PUT /api/equipment', () => {
    it('should bulk update equipment successfully', async () => {
      const updates = [
        { id: 'eq_001', name: 'Updated Camera' },
        { id: 'eq_002', name: 'Updated Mount' },
      ];

      const request = createMockRequest('PUT', 'http://localhost:3000/api/equipment', { updates });
      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(2);
      expect(data.data[0]).toMatchObject({ id: 'eq_001', success: true });
      expect(data.data[1]).toMatchObject({ id: 'eq_002', success: true });
    });

    it('should handle non-existent equipment in bulk update', async () => {
      const updates = [
        { id: 'eq_001', name: 'Updated Camera' },
        { id: 'non_existent', name: 'Non-existent Equipment' },
      ];

      const request = createMockRequest('PUT', 'http://localhost:3000/api/equipment', { updates });
      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(2);
      expect(data.data[0]).toMatchObject({ id: 'eq_001', success: true });
      expect(data.data[1]).toMatchObject({ id: 'non_existent', success: false, error: 'Equipment not found' });
    });

    it('should reject invalid bulk update format', async () => {
      const request = createMockRequest('PUT', 'http://localhost:3000/api/equipment', { updates: 'not-an-array' });
      const response = await PUT(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Updates must be an array');
    });
  });

  describe('DELETE /api/equipment', () => {
    it('should bulk delete equipment successfully', async () => {
      const ids = ['eq_001', 'eq_002'];

      const request = createMockRequest('DELETE', 'http://localhost:3000/api/equipment', { ids });
      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(2);
      expect(data.data[0]).toMatchObject({ id: 'eq_001', success: true });
      expect(data.data[1]).toMatchObject({ id: 'eq_002', success: true });
    });

    it('should handle non-existent equipment in bulk delete', async () => {
      const ids = ['eq_001', 'non_existent'];

      const request = createMockRequest('DELETE', 'http://localhost:3000/api/equipment', { ids });
      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data).toHaveLength(2);
      expect(data.data[0]).toMatchObject({ id: 'eq_001', success: true });
      expect(data.data[1]).toMatchObject({ id: 'non_existent', success: false, error: 'Equipment not found' });
    });

    it('should reject invalid bulk delete format', async () => {
      const request = createMockRequest('DELETE', 'http://localhost:3000/api/equipment', { ids: 'not-an-array' });
      const response = await DELETE(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toBe('IDs must be an array');
    });
  });

  describe('Error handling', () => {
    it('should handle internal server errors gracefully', async () => {
      // Mock an error in the equipment database
      const originalConsoleError = console.error;
      console.error = jest.fn();

      // This would cause an error in a real scenario
      const request = createMockRequest('GET');
      
      // Simulate an error by mocking a function that throws
      jest.spyOn(JSON, 'stringify').mockImplementationOnce(() => {
        throw new Error('Simulated error');
      });

      const response = await GET(request);
      const data = await response.json();

      expect(response.status).toBe(500);
      expect(data.success).toBe(false);
      expect(data.error).toBe('Failed to retrieve equipment');

      // Restore mocks
      jest.restoreAllMocks();
      console.error = originalConsoleError;
    });

    it('should log appropriate messages for different operations', async () => {
      const { logger } = require('@/lib/utils/logging');

      // Test GET request logging
      const getRequest = createMockRequest('GET');
      await GET(getRequest);

      expect(logger.info).toHaveBeenCalledWith(
        'Equipment list requested',
        expect.objectContaining({
          url: expect.any(String),
          userAgent: expect.any(String),
        })
      );

      expect(logger.info).toHaveBeenCalledWith(
        'Equipment list retrieved',
        expect.objectContaining({
          count: expect.any(Number),
          userId: 'user_001',
        })
      );
    });
  });

  describe('Data consistency', () => {
    it('should maintain consistent equipment data structure', async () => {
      const request = createMockRequest('GET');
      const response = await GET(request);
      const data = await response.json();

      data.data.forEach((equipment: any) => {
        // Check required fields
        expect(equipment).toHaveProperty('id');
        expect(equipment).toHaveProperty('name');
        expect(equipment).toHaveProperty('type');
        expect(equipment).toHaveProperty('manufacturer');
        expect(equipment).toHaveProperty('model');
        expect(equipment).toHaveProperty('status');
        expect(equipment).toHaveProperty('connectionType');
        expect(equipment).toHaveProperty('capabilities');
        expect(equipment).toHaveProperty('settings');
        expect(equipment).toHaveProperty('userId');
        expect(equipment).toHaveProperty('createdAt');
        expect(equipment).toHaveProperty('updatedAt');

        // Check data types
        expect(typeof equipment.id).toBe('string');
        expect(typeof equipment.name).toBe('string');
        expect(typeof equipment.type).toBe('string');
        expect(typeof equipment.status).toBe('string');
        expect(Array.isArray(equipment.capabilities)).toBe(true);
        expect(typeof equipment.settings).toBe('object');
      });
    });

    it('should include additional runtime data', async () => {
      const request = createMockRequest('GET');
      const response = await GET(request);
      const data = await response.json();

      data.data.forEach((equipment: any) => {
        expect(equipment).toHaveProperty('connectionHealth');
        expect(['good', 'poor']).toContain(equipment.connectionHealth);
      });
    });
  });
});
