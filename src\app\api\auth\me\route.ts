import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/auth/user-service';
import { requireAuth, getUserFromHeaders } from '@/lib/auth/middleware';

/**
 * GET /api/auth/me
 * Get current user profile
 */
export async function GET(request: NextRequest) {
  try {
    // Apply authentication middleware
    const authResult = await requireAuth()(request);
    if (authResult) {
      return authResult;
    }
    
    // Get user from headers (set by auth middleware)
    const userFromHeaders = getUserFromHeaders(request);
    if (!userFromHeaders) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found in request',
          code: 'USER_NOT_FOUND',
        },
        { status: 404 }
      );
    }
    
    // Get full user data from database
    const user = await UserService.findUserById(userFromHeaders.userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found',
          code: 'USER_NOT_FOUND',
        },
        { status: 404 }
      );
    }
    
    // Return user profile (without password hash)
    const { passwordHash: _, ...userProfile } = user;
    
    const response = NextResponse.json(
      {
        success: true,
        data: userProfile,
      },
      { status: 200 }
    );
    
    // Set security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    
    return response;
    
  } catch (error) {
    console.error('Get user profile error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to get user profile',
        code: 'PROFILE_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/me
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
