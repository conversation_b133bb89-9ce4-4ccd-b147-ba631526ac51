import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/auth/user-service';
import { rateLimit, validateInput, combineMiddleware } from '@/lib/auth/middleware';

// Input validation schema
const verifyEmailSchema = {
  token: { required: true, type: 'string', minLength: 1 },
};

/**
 * POST /api/auth/verify-email
 * Verify email address with token
 */
export async function POST(request: NextRequest) {
  try {
    // Apply middleware
    const middlewareResult = await combineMiddleware(
      rateLimit({ action: 'default' }),
      validateInput(verifyEmailSchema)
    )(request);
    
    if (middlewareResult) {
      return middlewareResult;
    }
    
    const body = await request.json();
    const { token } = body;
    
    // Verify email with token
    const success = await UserService.verifyEmail(token);
    
    if (!success) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid or expired verification token',
          code: 'INVALID_VERIFICATION_TOKEN',
        },
        { status: 400 }
      );
    }
    
    const response = NextResponse.json(
      {
        success: true,
        message: 'Email verified successfully',
      },
      { status: 200 }
    );
    
    // Set security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    
    return response;
    
  } catch (error) {
    console.error('Email verification error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Email verification failed',
        code: 'VERIFICATION_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/verify-email
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
