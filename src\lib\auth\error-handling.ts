import { NextResponse } from 'next/server';

// Error codes for authentication
export const AUTH_ERROR_CODES = {
  // Authentication errors
  INVALID_CREDENTIALS: 'INVALID_CREDENTIALS',
  UNAUTHORIZED: 'UNAUTHORIZED',
  FORBIDDEN: 'FORBIDDEN',
  TOKEN_EXPIRED: 'TOKEN_EXPIRED',
  INVALID_TOKEN: 'INVALID_TOKEN',
  INVALID_REFRESH_TOKEN: 'INVALID_REFRESH_TOKEN',
  
  // Registration errors
  EMAIL_EXISTS: 'EMAIL_EXISTS',
  USERNAME_EXISTS: 'USERNAME_EXISTS',
  WEAK_PASSWORD: 'WEAK_PASSWORD',
  REGISTRATION_ERROR: 'REGISTRATION_ERROR',
  
  // Validation errors
  VALIDATION_ERROR: 'VALIDATION_ERROR',
  INVALID_EMAIL: 'INVALID_EMAIL',
  INVALID_USERNAME: 'INVALID_USERNAME',
  MISSING_REQUIRED_FIELD: 'MISSING_REQUIRED_FIELD',
  
  // Email verification
  EMAIL_NOT_VERIFIED: 'EMAIL_NOT_VERIFIED',
  INVALID_VERIFICATION_TOKEN: 'INVALID_VERIFICATION_TOKEN',
  VERIFICATION_ERROR: 'VERIFICATION_ERROR',
  
  // Password reset
  INVALID_RESET_TOKEN: 'INVALID_RESET_TOKEN',
  RESET_TOKEN_EXPIRED: 'RESET_TOKEN_EXPIRED',
  RESET_ERROR: 'RESET_ERROR',
  
  // Rate limiting
  RATE_LIMIT_EXCEEDED: 'RATE_LIMIT_EXCEEDED',
  TOO_MANY_ATTEMPTS: 'TOO_MANY_ATTEMPTS',
  
  // Security
  CSRF_ERROR: 'CSRF_ERROR',
  SUSPICIOUS_ACTIVITY: 'SUSPICIOUS_ACTIVITY',
  ACCOUNT_LOCKED: 'ACCOUNT_LOCKED',
  
  // System errors
  INTERNAL_ERROR: 'INTERNAL_ERROR',
  DATABASE_ERROR: 'DATABASE_ERROR',
  NETWORK_ERROR: 'NETWORK_ERROR',
  SERVICE_UNAVAILABLE: 'SERVICE_UNAVAILABLE',
} as const;

// Error messages for user display (security-conscious)
export const AUTH_ERROR_MESSAGES = {
  [AUTH_ERROR_CODES.INVALID_CREDENTIALS]: 'Invalid email or password',
  [AUTH_ERROR_CODES.UNAUTHORIZED]: 'Authentication required',
  [AUTH_ERROR_CODES.FORBIDDEN]: 'Access denied',
  [AUTH_ERROR_CODES.TOKEN_EXPIRED]: 'Session expired. Please log in again',
  [AUTH_ERROR_CODES.INVALID_TOKEN]: 'Invalid session. Please log in again',
  [AUTH_ERROR_CODES.INVALID_REFRESH_TOKEN]: 'Session expired. Please log in again',
  
  [AUTH_ERROR_CODES.EMAIL_EXISTS]: 'An account with this email already exists',
  [AUTH_ERROR_CODES.USERNAME_EXISTS]: 'This username is already taken',
  [AUTH_ERROR_CODES.WEAK_PASSWORD]: 'Password does not meet security requirements',
  [AUTH_ERROR_CODES.REGISTRATION_ERROR]: 'Registration failed. Please try again',
  
  [AUTH_ERROR_CODES.VALIDATION_ERROR]: 'Please check your input and try again',
  [AUTH_ERROR_CODES.INVALID_EMAIL]: 'Please enter a valid email address',
  [AUTH_ERROR_CODES.INVALID_USERNAME]: 'Username must be 3-30 characters and contain only letters, numbers, underscores, and hyphens',
  [AUTH_ERROR_CODES.MISSING_REQUIRED_FIELD]: 'Please fill in all required fields',
  
  [AUTH_ERROR_CODES.EMAIL_NOT_VERIFIED]: 'Please verify your email address to continue',
  [AUTH_ERROR_CODES.INVALID_VERIFICATION_TOKEN]: 'Invalid or expired verification link',
  [AUTH_ERROR_CODES.VERIFICATION_ERROR]: 'Email verification failed. Please try again',
  
  [AUTH_ERROR_CODES.INVALID_RESET_TOKEN]: 'Invalid or expired password reset link',
  [AUTH_ERROR_CODES.RESET_TOKEN_EXPIRED]: 'Password reset link has expired',
  [AUTH_ERROR_CODES.RESET_ERROR]: 'Password reset failed. Please try again',
  
  [AUTH_ERROR_CODES.RATE_LIMIT_EXCEEDED]: 'Too many requests. Please try again later',
  [AUTH_ERROR_CODES.TOO_MANY_ATTEMPTS]: 'Too many failed attempts. Please try again later',
  
  [AUTH_ERROR_CODES.CSRF_ERROR]: 'Security validation failed. Please refresh and try again',
  [AUTH_ERROR_CODES.SUSPICIOUS_ACTIVITY]: 'Suspicious activity detected. Please contact support',
  [AUTH_ERROR_CODES.ACCOUNT_LOCKED]: 'Account temporarily locked due to security concerns',
  
  [AUTH_ERROR_CODES.INTERNAL_ERROR]: 'Something went wrong. Please try again',
  [AUTH_ERROR_CODES.DATABASE_ERROR]: 'Service temporarily unavailable. Please try again',
  [AUTH_ERROR_CODES.NETWORK_ERROR]: 'Network error. Please check your connection',
  [AUTH_ERROR_CODES.SERVICE_UNAVAILABLE]: 'Service temporarily unavailable. Please try again later',
} as const;

// Error severity levels
export enum ErrorSeverity {
  LOW = 'low',
  MEDIUM = 'medium',
  HIGH = 'high',
  CRITICAL = 'critical',
}

// Error categories for logging and monitoring
export enum ErrorCategory {
  AUTHENTICATION = 'authentication',
  AUTHORIZATION = 'authorization',
  VALIDATION = 'validation',
  SECURITY = 'security',
  SYSTEM = 'system',
  NETWORK = 'network',
}

// Enhanced error class
export class AuthError extends Error {
  public readonly code: string;
  public readonly statusCode: number;
  public readonly severity: ErrorSeverity;
  public readonly category: ErrorCategory;
  public readonly userMessage: string;
  public readonly details?: any;
  public readonly timestamp: Date;

  constructor(
    code: string,
    message: string,
    statusCode: number = 500,
    severity: ErrorSeverity = ErrorSeverity.MEDIUM,
    category: ErrorCategory = ErrorCategory.SYSTEM,
    details?: any
  ) {
    super(message);
    this.name = 'AuthError';
    this.code = code;
    this.statusCode = statusCode;
    this.severity = severity;
    this.category = category;
    this.userMessage = AUTH_ERROR_MESSAGES[code as keyof typeof AUTH_ERROR_MESSAGES] || 'An error occurred';
    this.details = details;
    this.timestamp = new Date();
  }

  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      userMessage: this.userMessage,
      statusCode: this.statusCode,
      severity: this.severity,
      category: this.category,
      details: this.details,
      timestamp: this.timestamp,
    };
  }
}

// Error factory functions
export class AuthErrorFactory {
  static invalidCredentials(details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.INVALID_CREDENTIALS,
      'Invalid email or password provided',
      401,
      ErrorSeverity.MEDIUM,
      ErrorCategory.AUTHENTICATION,
      details
    );
  }

  static unauthorized(details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.UNAUTHORIZED,
      'Authentication required',
      401,
      ErrorSeverity.MEDIUM,
      ErrorCategory.AUTHENTICATION,
      details
    );
  }

  static forbidden(details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.FORBIDDEN,
      'Access denied - insufficient permissions',
      403,
      ErrorSeverity.MEDIUM,
      ErrorCategory.AUTHORIZATION,
      details
    );
  }

  static tokenExpired(details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.TOKEN_EXPIRED,
      'Authentication token has expired',
      401,
      ErrorSeverity.LOW,
      ErrorCategory.AUTHENTICATION,
      details
    );
  }

  static emailExists(details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.EMAIL_EXISTS,
      'Email address already registered',
      409,
      ErrorSeverity.LOW,
      ErrorCategory.VALIDATION,
      details
    );
  }

  static usernameExists(details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.USERNAME_EXISTS,
      'Username already taken',
      409,
      ErrorSeverity.LOW,
      ErrorCategory.VALIDATION,
      details
    );
  }

  static weakPassword(details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.WEAK_PASSWORD,
      'Password does not meet security requirements',
      400,
      ErrorSeverity.MEDIUM,
      ErrorCategory.VALIDATION,
      details
    );
  }

  static validationError(message: string, details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.VALIDATION_ERROR,
      message,
      400,
      ErrorSeverity.LOW,
      ErrorCategory.VALIDATION,
      details
    );
  }

  static rateLimitExceeded(retryAfter?: number): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.RATE_LIMIT_EXCEEDED,
      'Rate limit exceeded',
      429,
      ErrorSeverity.MEDIUM,
      ErrorCategory.SECURITY,
      { retryAfter }
    );
  }

  static csrfError(details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.CSRF_ERROR,
      'CSRF token validation failed',
      403,
      ErrorSeverity.HIGH,
      ErrorCategory.SECURITY,
      details
    );
  }

  static internalError(message: string = 'Internal server error', details?: any): AuthError {
    return new AuthError(
      AUTH_ERROR_CODES.INTERNAL_ERROR,
      message,
      500,
      ErrorSeverity.HIGH,
      ErrorCategory.SYSTEM,
      details
    );
  }
}

// Error response builder
export class ErrorResponseBuilder {
  static buildErrorResponse(error: AuthError | Error): NextResponse {
    if (error instanceof AuthError) {
      return NextResponse.json(
        {
          success: false,
          error: error.userMessage,
          code: error.code,
          details: error.details,
        },
        { status: error.statusCode }
      );
    }

    // Handle generic errors
    console.error('Unhandled error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'An unexpected error occurred',
        code: AUTH_ERROR_CODES.INTERNAL_ERROR,
      },
      { status: 500 }
    );
  }

  static buildValidationErrorResponse(errors: Record<string, string[]>): NextResponse {
    return NextResponse.json(
      {
        success: false,
        error: 'Validation failed',
        code: AUTH_ERROR_CODES.VALIDATION_ERROR,
        details: errors,
      },
      { status: 400 }
    );
  }

  static buildRateLimitResponse(retryAfter: number): NextResponse {
    return NextResponse.json(
      {
        success: false,
        error: 'Too many requests',
        code: AUTH_ERROR_CODES.RATE_LIMIT_EXCEEDED,
        retryAfter,
      },
      { 
        status: 429,
        headers: {
          'Retry-After': retryAfter.toString(),
        },
      }
    );
  }
}

// Error logger
export class ErrorLogger {
  static logError(error: AuthError | Error, context?: Record<string, any>) {
    const logData = {
      timestamp: new Date().toISOString(),
      error: error instanceof AuthError ? error.toJSON() : {
        name: error.name,
        message: error.message,
        stack: error.stack,
      },
      context,
    };

    // In production, send to logging service
    if (process.env.NODE_ENV === 'production') {
      // Send to external logging service (e.g., Sentry, LogRocket, etc.)
      console.error('AUTH_ERROR:', JSON.stringify(logData));
    } else {
      console.error('AUTH_ERROR:', logData);
    }

    // Log security-related errors with higher priority
    if (error instanceof AuthError && error.severity === ErrorSeverity.HIGH) {
      // Send security alerts
      console.warn('SECURITY_ALERT:', logData);
    }
  }
}
