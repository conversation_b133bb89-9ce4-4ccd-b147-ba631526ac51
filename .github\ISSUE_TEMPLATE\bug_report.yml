name: 🐛 Bug Report
description: Report a bug or issue with Cobalt Mobile
title: "[Bug]: "
labels: ["bug", "needs-triage"]
assignees: []

body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to report a bug! Please fill out the form below with as much detail as possible.

  - type: textarea
    id: description
    attributes:
      label: Bug Description
      description: A clear and concise description of what the bug is.
      placeholder: Describe the bug...
    validations:
      required: true

  - type: textarea
    id: steps
    attributes:
      label: Steps to Reproduce
      description: Steps to reproduce the behavior
      placeholder: |
        1. Go to '...'
        2. Click on '...'
        3. Scroll down to '...'
        4. See error
    validations:
      required: true

  - type: textarea
    id: expected
    attributes:
      label: Expected Behavior
      description: A clear and concise description of what you expected to happen.
      placeholder: What should have happened?
    validations:
      required: true

  - type: textarea
    id: actual
    attributes:
      label: Actual Behavior
      description: A clear and concise description of what actually happened.
      placeholder: What actually happened?
    validations:
      required: true

  - type: textarea
    id: screenshots
    attributes:
      label: Screenshots
      description: If applicable, add screenshots to help explain your problem.
      placeholder: Drag and drop screenshots here...

  - type: dropdown
    id: device-type
    attributes:
      label: Device Type
      description: What type of device are you using?
      options:
        - Mobile Phone
        - Tablet
        - Desktop
        - Other
    validations:
      required: true

  - type: input
    id: os
    attributes:
      label: Operating System
      description: What operating system are you using?
      placeholder: e.g., iOS 17.0, Android 14, Windows 11, macOS 14
    validations:
      required: true

  - type: input
    id: browser
    attributes:
      label: Browser
      description: What browser are you using?
      placeholder: e.g., Chrome 119, Safari 17, Firefox 119
    validations:
      required: true

  - type: input
    id: version
    attributes:
      label: App Version
      description: What version of Cobalt Mobile are you using?
      placeholder: e.g., 1.0.0
    validations:
      required: true

  - type: textarea
    id: equipment
    attributes:
      label: Equipment Configuration
      description: If the bug is related to equipment control, please describe your setup.
      placeholder: |
        - Camera: ...
        - Mount: ...
        - Focuser: ...
        - Filter Wheel: ...

  - type: textarea
    id: logs
    attributes:
      label: Console Logs
      description: If applicable, include any console logs or error messages.
      placeholder: Paste console logs here...

  - type: textarea
    id: additional
    attributes:
      label: Additional Context
      description: Add any other context about the problem here.
      placeholder: Any additional information...

  - type: checkboxes
    id: terms
    attributes:
      label: Checklist
      description: Please confirm the following
      options:
        - label: I have searched existing issues to ensure this is not a duplicate
          required: true
        - label: I have provided all the requested information
          required: true
        - label: I am willing to help test a fix for this issue
          required: false
