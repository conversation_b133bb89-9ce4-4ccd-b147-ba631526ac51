/**
 * @jest-environment jsdom
 */

import { UserService } from '../user-service';
import { USER_ROLES } from '../auth-utils';

describe('UserService', () => {
  beforeEach(() => {
    // Clear any existing users before each test
    // Note: In a real implementation, you'd reset the database
  });

  describe('createUser', () => {
    const validUserData = {
      email: '<EMAIL>',
      password: 'StrongP@ssw0rd123',
      username: 'testuser',
      firstName: 'Test',
      lastName: 'User',
      timezone: 'UTC',
    };

    it('should create a new user successfully', async () => {
      const user = await UserService.createUser(validUserData);
      
      expect(user).toBeDefined();
      expect(user.email).toBe(validUserData.email.toLowerCase());
      expect(user.username).toBe(validUserData.username);
      expect(user.firstName).toBe(validUserData.firstName);
      expect(user.lastName).toBe(validUserData.lastName);
      expect(user.role).toBe(USER_ROLES.USER);
      expect(user.emailVerified).toBe(false);
      expect(user.id).toBeDefined();
      expect(user.createdAt).toBeDefined();
      expect(user.updatedAt).toBeDefined();
    });

    it('should reject duplicate email', async () => {
      await UserService.createUser(validUserData);
      
      await expect(UserService.createUser({
        ...validUserData,
        username: 'different',
      })).rejects.toThrow('User with this email already exists');
    });

    it('should reject duplicate username', async () => {
      await UserService.createUser(validUserData);
      
      await expect(UserService.createUser({
        ...validUserData,
        email: '<EMAIL>',
      })).rejects.toThrow('Username is already taken');
    });

    it('should reject weak password', async () => {
      await expect(UserService.createUser({
        ...validUserData,
        password: 'weak',
      })).rejects.toThrow('Password validation failed');
    });

    it('should store email in lowercase', async () => {
      const user = await UserService.createUser({
        ...validUserData,
        email: '<EMAIL>',
      });
      
      expect(user.email).toBe('<EMAIL>');
    });
  });

  describe('findUserByEmail', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'StrongP@ssw0rd123',
      username: 'finduser',
      firstName: 'Find',
      lastName: 'User',
      timezone: 'UTC',
    };

    it('should find existing user by email', async () => {
      await UserService.createUser(userData);
      
      const user = await UserService.findUserByEmail(userData.email);
      expect(user).toBeDefined();
      expect(user?.email).toBe(userData.email);
    });

    it('should find user with case-insensitive email', async () => {
      await UserService.createUser(userData);
      
      const user = await UserService.findUserByEmail('<EMAIL>');
      expect(user).toBeDefined();
      expect(user?.email).toBe(userData.email);
    });

    it('should return null for non-existent email', async () => {
      const user = await UserService.findUserByEmail('<EMAIL>');
      expect(user).toBeNull();
    });
  });

  describe('authenticateUser', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'StrongP@ssw0rd123',
      username: 'authuser',
      firstName: 'Auth',
      lastName: 'User',
      timezone: 'UTC',
    };

    beforeEach(async () => {
      await UserService.createUser(userData);
    });

    it('should authenticate user with correct credentials', async () => {
      const user = await UserService.authenticateUser(userData.email, userData.password);
      expect(user).toBeDefined();
      expect(user?.email).toBe(userData.email);
    });

    it('should return null for incorrect password', async () => {
      const user = await UserService.authenticateUser(userData.email, 'wrongpassword');
      expect(user).toBeNull();
    });

    it('should return null for non-existent email', async () => {
      const user = await UserService.authenticateUser('<EMAIL>', userData.password);
      expect(user).toBeNull();
    });
  });

  describe('changePassword', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'OldP@ssw0rd123',
      username: 'passworduser',
      firstName: 'Password',
      lastName: 'User',
      timezone: 'UTC',
    };

    let userId: string;

    beforeEach(async () => {
      const user = await UserService.createUser(userData);
      userId = user.id;
    });

    it('should change password with correct current password', async () => {
      const newPassword = 'NewP@ssw0rd456';
      const result = await UserService.changePassword(userId, userData.password, newPassword);
      expect(result).toBe(true);

      // Verify new password works
      const user = await UserService.authenticateUser(userData.email, newPassword);
      expect(user).toBeDefined();

      // Verify old password doesn't work
      const oldAuth = await UserService.authenticateUser(userData.email, userData.password);
      expect(oldAuth).toBeNull();
    });

    it('should reject incorrect current password', async () => {
      const result = await UserService.changePassword(userId, 'wrongpassword', 'NewP@ssw0rd456');
      expect(result).toBe(false);
    });

    it('should reject weak new password', async () => {
      await expect(UserService.changePassword(userId, userData.password, 'weak')).rejects.toThrow('Password validation failed');
    });

    it('should return false for non-existent user', async () => {
      const result = await UserService.changePassword('nonexistent', userData.password, 'NewP@ssw0rd456');
      expect(result).toBe(false);
    });
  });

  describe('verifyEmail', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'StrongP@ssw0rd123',
      username: 'verifyuser',
      firstName: 'Verify',
      lastName: 'User',
      timezone: 'UTC',
    };

    it('should verify email with valid token', async () => {
      const user = await UserService.createUser(userData);
      
      // Get the verification token (in real implementation, this would be sent via email)
      const userWithToken = await UserService.findUserById(user.id);
      const token = userWithToken?.emailVerificationToken;
      
      expect(token).toBeDefined();
      
      const result = await UserService.verifyEmail(token!);
      expect(result).toBe(true);
      
      // Check that user is now verified
      const verifiedUser = await UserService.findUserById(user.id);
      expect(verifiedUser?.emailVerified).toBe(true);
    });

    it('should return false for invalid token', async () => {
      const result = await UserService.verifyEmail('invalid-token');
      expect(result).toBe(false);
    });
  });

  describe('generatePasswordResetToken', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'StrongP@ssw0rd123',
      username: 'resetuser',
      firstName: 'Reset',
      lastName: 'User',
      timezone: 'UTC',
    };

    beforeEach(async () => {
      await UserService.createUser(userData);
    });

    it('should generate reset token for existing user', async () => {
      const token = await UserService.generatePasswordResetToken(userData.email);
      expect(token).toBeDefined();
      expect(typeof token).toBe('string');
      expect(token!.length).toBeGreaterThan(0);
    });

    it('should return null for non-existent email', async () => {
      const token = await UserService.generatePasswordResetToken('<EMAIL>');
      expect(token).toBeNull();
    });
  });

  describe('resetPassword', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'OldP@ssw0rd123',
      username: 'passwordresetuser',
      firstName: 'PasswordReset',
      lastName: 'User',
      timezone: 'UTC',
    };

    let resetToken: string;

    beforeEach(async () => {
      await UserService.createUser(userData);
      resetToken = (await UserService.generatePasswordResetToken(userData.email))!;
    });

    it('should reset password with valid token', async () => {
      const newPassword = 'NewP@ssw0rd456';
      const result = await UserService.resetPassword(resetToken, newPassword);
      expect(result).toBe(true);

      // Verify new password works
      const user = await UserService.authenticateUser(userData.email, newPassword);
      expect(user).toBeDefined();
    });

    it('should return false for invalid token', async () => {
      const result = await UserService.resetPassword('invalid-token', 'NewP@ssw0rd456');
      expect(result).toBe(false);
    });

    it('should reject weak new password', async () => {
      await expect(UserService.resetPassword(resetToken, 'weak')).rejects.toThrow('Password validation failed');
    });
  });

  describe('refresh token management', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'StrongP@ssw0rd123',
      username: 'tokenuser',
      firstName: 'Token',
      lastName: 'User',
      timezone: 'UTC',
    };

    let userId: string;

    beforeEach(async () => {
      const user = await UserService.createUser(userData);
      userId = user.id;
    });

    it('should store and validate refresh token', async () => {
      const token = 'test-refresh-token';
      
      await UserService.storeRefreshToken(token, userId);
      
      const validatedUserId = await UserService.validateRefreshToken(token);
      expect(validatedUserId).toBe(userId);
    });

    it('should return null for invalid refresh token', async () => {
      const validatedUserId = await UserService.validateRefreshToken('invalid-token');
      expect(validatedUserId).toBeNull();
    });

    it('should revoke refresh token', async () => {
      const token = 'test-refresh-token';
      
      await UserService.storeRefreshToken(token, userId);
      await UserService.revokeRefreshToken(token);
      
      const validatedUserId = await UserService.validateRefreshToken(token);
      expect(validatedUserId).toBeNull();
    });
  });

  describe('deleteUser', () => {
    const userData = {
      email: '<EMAIL>',
      password: 'StrongP@ssw0rd123',
      username: 'deleteuser',
      firstName: 'Delete',
      lastName: 'User',
      timezone: 'UTC',
    };

    it('should delete user and cleanup tokens', async () => {
      const user = await UserService.createUser(userData);
      const userId = user.id;
      
      // Store some tokens
      await UserService.storeRefreshToken('refresh-token', userId);
      await UserService.generatePasswordResetToken(userData.email);
      
      const result = await UserService.deleteUser(userId);
      expect(result).toBe(true);
      
      // Verify user is deleted
      const deletedUser = await UserService.findUserById(userId);
      expect(deletedUser).toBeNull();
      
      // Verify tokens are cleaned up
      const tokenValidation = await UserService.validateRefreshToken('refresh-token');
      expect(tokenValidation).toBeNull();
    });

    it('should return false for non-existent user', async () => {
      const result = await UserService.deleteUser('nonexistent');
      expect(result).toBe(false);
    });
  });
});
