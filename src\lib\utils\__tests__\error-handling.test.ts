/**
 * @jest-environment jsdom
 */

import { 
  createError, 
  handleError, 
  logError, 
  withErrorHandling, 
  ErrorCodes,
  type EnhancedError,
  type RecoveryOptions 
} from '../error-handling';

// Mock console methods
const mockConsoleError = jest.spyOn(console, 'error').mockImplementation();
const mockConsoleWarn = jest.spyOn(console, 'warn').mockImplementation();
const mockConsoleInfo = jest.spyOn(console, 'info').mockImplementation();

// Mock window and navigator
Object.defineProperty(window, 'location', {
  value: { href: 'http://localhost:3000/test' },
  writable: true,
});

Object.defineProperty(navigator, 'userAgent', {
  value: 'Mozilla/5.0 (Test Browser)',
  writable: true,
});

describe('Error Handling Utilities', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('createError', () => {
    it('should create enhanced error with default values', () => {
      const error = createError('Test error message');
      
      expect(error).toBeInstanceOf(Error);
      expect(error.message).toBe('Test error message');
      expect(error.code).toBe('UNKNOWN_ERROR');
      expect(error.severity).toBe('medium');
      expect(error.category).toBe('system');
      expect(error.retryable).toBe(true);
      expect(error.timestamp).toBeInstanceOf(Date);
      expect(error.suggestions).toEqual([]);
    });

    it('should create enhanced error with custom options', () => {
      const error = createError('Custom error', {
        code: 'CUSTOM_ERROR',
        severity: 'high',
        category: 'network',
        retryable: false,
        suggestions: ['Check connection', 'Retry later'],
        context: { userId: '123', action: 'test' },
      });
      
      expect(error.code).toBe('CUSTOM_ERROR');
      expect(error.severity).toBe('high');
      expect(error.category).toBe('network');
      expect(error.retryable).toBe(false);
      expect(error.suggestions).toEqual(['Check connection', 'Retry later']);
      expect(error.context).toEqual({ userId: '123', action: 'test' });
    });
  });

  describe('logError', () => {
    it('should log error with appropriate console method based on severity', () => {
      const criticalError = createError('Critical error', { severity: 'critical' });
      const highError = createError('High error', { severity: 'high' });
      const mediumError = createError('Medium error', { severity: 'medium' });
      const lowError = createError('Low error', { severity: 'low' });

      logError(criticalError);
      logError(highError);
      logError(mediumError);
      logError(lowError);

      expect(mockConsoleError).toHaveBeenCalledTimes(2); // critical and high
      expect(mockConsoleWarn).toHaveBeenCalledTimes(1);  // medium
      expect(mockConsoleInfo).toHaveBeenCalledTimes(1);  // low
    });

    it('should include context information in log', () => {
      const error = createError('Test error', {
        code: 'TEST_ERROR',
        category: 'network',
        context: { requestId: '123' },
      });

      logError(error);

      expect(mockConsoleWarn).toHaveBeenCalledWith(
        'MEDIUM SEVERITY ERROR:',
        expect.objectContaining({
          message: 'Test error',
          code: 'TEST_ERROR',
          category: 'network',
          context: { requestId: '123' },
          userAgent: 'Mozilla/5.0 (Test Browser)',
          url: 'http://localhost:3000/test',
        })
      );
    });
  });

  describe('handleError', () => {
    it('should handle error without recovery options', async () => {
      const error = createError('Test error');
      const result = await handleError(error);
      
      expect(result).toBeNull();
      expect(mockConsoleWarn).toHaveBeenCalled();
    });

    it('should handle error with fallback recovery', async () => {
      const error = createError('Test error');
      const recovery: RecoveryOptions = {
        strategy: 'fallback',
        fallbackValue: 'fallback result',
      };
      
      const result = await handleError(error, recovery);
      
      expect(result).toBe('fallback result');
    });

    it('should handle error with ignore recovery', async () => {
      const error = createError('Test error');
      const recovery: RecoveryOptions = {
        strategy: 'ignore',
      };
      
      const result = await handleError(error, recovery);
      
      expect(result).toBeNull();
    });
  });

  describe('withErrorHandling', () => {
    it('should wrap async function and handle success', async () => {
      const mockFn = jest.fn().mockResolvedValue('success result');
      const wrappedFn = withErrorHandling(mockFn);
      
      const result = await wrappedFn('arg1', 'arg2');
      
      expect(result).toBe('success result');
      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('should wrap async function and handle errors', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Test error'));
      const wrappedFn = withErrorHandling(mockFn);
      
      const result = await wrappedFn();
      
      expect(result).toBeNull();
      expect(mockConsoleWarn).toHaveBeenCalled();
    });

    it('should wrap async function with recovery options', async () => {
      const mockFn = jest.fn().mockRejectedValue(new Error('Test error'));
      const recovery: RecoveryOptions = {
        strategy: 'fallback',
        fallbackValue: 'recovery result',
      };
      const wrappedFn = withErrorHandling(mockFn, recovery);
      
      const result = await wrappedFn();
      
      expect(result).toBe('recovery result');
    });
  });

  describe('ErrorCodes', () => {
    it('should contain all expected error codes', () => {
      expect(ErrorCodes.NETWORK_ERROR).toBe('NETWORK_ERROR');
      expect(ErrorCodes.TIMEOUT_ERROR).toBe('TIMEOUT_ERROR');
      expect(ErrorCodes.VALIDATION_ERROR).toBe('VALIDATION_ERROR');
      expect(ErrorCodes.EQUIPMENT_TIMEOUT).toBe('EQUIPMENT_TIMEOUT');
      expect(ErrorCodes.CAMERA_ERROR).toBe('CAMERA_ERROR');
      expect(ErrorCodes.MOUNT_ERROR).toBe('MOUNT_ERROR');
      expect(ErrorCodes.FOCUSER_ERROR).toBe('FOCUSER_ERROR');
      expect(ErrorCodes.FILTERWHEEL_ERROR).toBe('FILTERWHEEL_ERROR');
    });
  });

  describe('Error categorization', () => {
    it('should properly categorize network errors', () => {
      const error = createError('Connection failed', {
        code: ErrorCodes.NETWORK_ERROR,
        category: 'network',
      });
      
      expect(error.category).toBe('network');
      expect(error.code).toBe('NETWORK_ERROR');
    });

    it('should properly categorize equipment errors', () => {
      const error = createError('Camera not responding', {
        code: ErrorCodes.CAMERA_ERROR,
        category: 'equipment',
      });
      
      expect(error.category).toBe('equipment');
      expect(error.code).toBe('CAMERA_ERROR');
    });

    it('should properly categorize validation errors', () => {
      const error = createError('Invalid input', {
        code: ErrorCodes.VALIDATION_ERROR,
        category: 'validation',
      });
      
      expect(error.category).toBe('validation');
      expect(error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('Error severity handling', () => {
    it('should handle critical errors appropriately', () => {
      const error = createError('System failure', { severity: 'critical' });
      logError(error);
      
      expect(mockConsoleError).toHaveBeenCalledWith(
        'CRITICAL ERROR:',
        expect.any(Object)
      );
    });

    it('should handle low severity errors appropriately', () => {
      const error = createError('Minor issue', { severity: 'low' });
      logError(error);
      
      expect(mockConsoleInfo).toHaveBeenCalledWith(
        'LOW SEVERITY ERROR:',
        expect.any(Object)
      );
    });
  });

  describe('Error context and metadata', () => {
    it('should preserve error context', () => {
      const context = {
        userId: 'user123',
        sessionId: 'session456',
        action: 'equipment_connect',
        equipmentId: 'camera001',
      };
      
      const error = createError('Equipment connection failed', {
        context,
        category: 'equipment',
      });
      
      expect(error.context).toEqual(context);
    });

    it('should include timestamp in error', () => {
      const beforeTime = new Date();
      const error = createError('Test error');
      const afterTime = new Date();
      
      expect(error.timestamp).toBeInstanceOf(Date);
      expect(error.timestamp!.getTime()).toBeGreaterThanOrEqual(beforeTime.getTime());
      expect(error.timestamp!.getTime()).toBeLessThanOrEqual(afterTime.getTime());
    });
  });

  describe('Error suggestions', () => {
    it('should include helpful suggestions', () => {
      const suggestions = [
        'Check your internet connection',
        'Verify equipment is powered on',
        'Try reconnecting the device',
      ];
      
      const error = createError('Connection failed', {
        suggestions,
        category: 'equipment',
      });
      
      expect(error.suggestions).toEqual(suggestions);
    });
  });

  describe('Retryable errors', () => {
    it('should mark network errors as retryable by default', () => {
      const error = createError('Network timeout', {
        category: 'network',
      });
      
      expect(error.retryable).toBe(true);
    });

    it('should allow marking errors as non-retryable', () => {
      const error = createError('Invalid credentials', {
        category: 'security',
        retryable: false,
      });
      
      expect(error.retryable).toBe(false);
    });
  });
});
