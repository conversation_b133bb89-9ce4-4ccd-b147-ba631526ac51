import { NextRequest, NextResponse } from 'next/server';
import { UserService } from '@/lib/auth/user-service';
import { JWTUtils } from '@/lib/auth/auth-utils';
import { rateLimit } from '@/lib/auth/middleware';

/**
 * POST /api/auth/refresh
 * Refresh access token using refresh token
 */
export async function POST(request: NextRequest) {
  try {
    // Apply rate limiting
    const rateLimitResult = rateLimit({ action: 'default' })(request);
    if (rateLimitResult) {
      return rateLimitResult;
    }
    
    // Get refresh token from request body or cookies
    let refreshToken: string | null = null;
    
    try {
      const body = await request.json();
      refreshToken = body.refreshToken;
    } catch {
      // If no JSON body, try to get from cookies
      refreshToken = request.cookies.get('refresh_token')?.value || null;
    }
    
    if (!refreshToken) {
      return NextResponse.json(
        {
          success: false,
          error: 'Refresh token is required',
          code: 'MISSING_REFRESH_TOKEN',
        },
        { status: 400 }
      );
    }
    
    // Verify refresh token
    const tokenPayload = JWTUtils.verifyRefreshToken(refreshToken);
    if (!tokenPayload) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid or expired refresh token',
          code: 'INVALID_REFRESH_TOKEN',
        },
        { status: 401 }
      );
    }
    
    // Validate refresh token in database
    const userId = await UserService.validateRefreshToken(refreshToken);
    if (!userId || userId !== tokenPayload.userId) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid refresh token',
          code: 'INVALID_REFRESH_TOKEN',
        },
        { status: 401 }
      );
    }
    
    // Get user data
    const user = await UserService.findUserById(userId);
    if (!user) {
      return NextResponse.json(
        {
          success: false,
          error: 'User not found',
          code: 'USER_NOT_FOUND',
        },
        { status: 404 }
      );
    }
    
    // Generate new tokens
    const newAccessToken = JWTUtils.generateAccessToken({
      userId: user.id,
      email: user.email,
      role: user.role,
      permissions: user.permissions,
    });
    
    const newRefreshToken = JWTUtils.generateRefreshToken(user.id);
    
    // Revoke old refresh token and store new one
    await UserService.revokeRefreshToken(refreshToken);
    await UserService.storeRefreshToken(newRefreshToken, user.id);
    
    // Calculate expiration time
    const expiresAt = new Date(Date.now() + 15 * 60 * 1000); // 15 minutes
    
    // Prepare response
    const response = NextResponse.json(
      {
        success: true,
        data: {
          user: {
            id: user.id,
            email: user.email,
            username: user.username,
            firstName: user.firstName,
            lastName: user.lastName,
            avatar: user.avatar,
            bio: user.bio,
            location: user.location,
            timezone: user.timezone,
            createdAt: user.createdAt,
            updatedAt: user.updatedAt,
          },
          token: newAccessToken,
          refreshToken: newRefreshToken,
          expiresAt: expiresAt.toISOString(),
          permissions: user.permissions,
        },
        message: 'Token refreshed successfully',
      },
      { status: 200 }
    );
    
    // Update refresh token cookie if it was set
    const cookieRefreshToken = request.cookies.get('refresh_token');
    if (cookieRefreshToken) {
      response.cookies.set('refresh_token', newRefreshToken, {
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'strict',
        maxAge: 7 * 24 * 60 * 60, // 7 days
        path: '/',
      });
    }
    
    // Set security headers
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
    response.headers.set('X-XSS-Protection', '1; mode=block');
    
    return response;
    
  } catch (error) {
    console.error('Token refresh error:', error);
    
    return NextResponse.json(
      {
        success: false,
        error: 'Token refresh failed',
        code: 'REFRESH_ERROR',
      },
      { status: 500 }
    );
  }
}

/**
 * OPTIONS /api/auth/refresh
 * Handle CORS preflight requests
 */
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
