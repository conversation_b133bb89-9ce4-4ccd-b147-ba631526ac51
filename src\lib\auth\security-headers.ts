import { NextResponse } from 'next/server';
import crypto from 'crypto';

// Security headers configuration
export const SECURITY_HEADERS = {
  // Prevent MIME type sniffing
  'X-Content-Type-Options': 'nosniff',
  
  // Prevent clickjacking
  'X-Frame-Options': 'DENY',
  
  // XSS protection
  'X-XSS-Protection': '1; mode=block',
  
  // Referrer policy
  'Referrer-Policy': 'strict-origin-when-cross-origin',
  
  // Permissions policy
  'Permissions-Policy': 'camera=(), microphone=(), geolocation=(), payment=()',
  
  // Strict transport security (HTTPS only)
  'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
  
  // Content security policy
  'Content-Security-Policy': `
    default-src 'self';
    script-src 'self' 'unsafe-eval' 'unsafe-inline';
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: https:;
    font-src 'self' data:;
    connect-src 'self' https://api.cobalt-mobile.com wss://ws.cobalt-mobile.com;
    frame-ancestors 'none';
    base-uri 'self';
    form-action 'self';
  `.replace(/\s+/g, ' ').trim(),
} as const;

// CSRF token management
export class CSRFProtection {
  private static readonly TOKEN_LENGTH = 32;
  private static readonly TOKEN_HEADER = 'x-csrf-token';
  private static readonly COOKIE_NAME = 'csrf-token';
  
  /**
   * Generate a CSRF token
   */
  static generateToken(): string {
    return crypto.randomBytes(this.TOKEN_LENGTH).toString('hex');
  }
  
  /**
   * Create CSRF token hash for verification
   */
  static createTokenHash(token: string, secret: string): string {
    return crypto.createHmac('sha256', secret).update(token).digest('hex');
  }
  
  /**
   * Verify CSRF token
   */
  static verifyToken(token: string, hash: string, secret: string): boolean {
    const expectedHash = this.createTokenHash(token, secret);
    return crypto.timingSafeEqual(Buffer.from(hash), Buffer.from(expectedHash));
  }
  
  /**
   * Set CSRF token in response
   */
  static setCSRFToken(response: NextResponse, token: string): void {
    response.cookies.set(this.COOKIE_NAME, token, {
      httpOnly: false, // Client needs to read this for AJAX requests
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict',
      maxAge: 60 * 60, // 1 hour
      path: '/',
    });
    
    response.headers.set(this.TOKEN_HEADER, token);
  }
  
  /**
   * Get CSRF token from request
   */
  static getTokenFromRequest(request: Request): string | null {
    // Try header first
    const headerToken = request.headers.get(this.TOKEN_HEADER);
    if (headerToken) return headerToken;
    
    // Try cookie
    const cookieHeader = request.headers.get('cookie');
    if (cookieHeader) {
      const cookies = cookieHeader.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        acc[key] = value;
        return acc;
      }, {} as Record<string, string>);
      
      return cookies[this.COOKIE_NAME] || null;
    }
    
    return null;
  }
}

// Security middleware
export class SecurityMiddleware {
  /**
   * Apply security headers to response
   */
  static applySecurityHeaders(response: NextResponse): NextResponse {
    Object.entries(SECURITY_HEADERS).forEach(([key, value]) => {
      response.headers.set(key, value);
    });
    
    return response;
  }
  
  /**
   * Create secure response with headers
   */
  static createSecureResponse(data: any, status: number = 200): NextResponse {
    const response = NextResponse.json(data, { status });
    return this.applySecurityHeaders(response);
  }
  
  /**
   * CSRF protection middleware
   */
  static csrfProtection(secret: string = process.env.CSRF_SECRET || 'default-csrf-secret') {
    return (request: Request): NextResponse | null => {
      // Skip CSRF for GET, HEAD, OPTIONS requests
      if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
        return null;
      }
      
      const token = CSRFProtection.getTokenFromRequest(request);
      const sessionToken = request.headers.get('x-session-csrf-token');
      
      if (!token || !sessionToken) {
        return NextResponse.json(
          {
            success: false,
            error: 'CSRF token missing',
            code: 'CSRF_ERROR',
          },
          { status: 403 }
        );
      }
      
      if (!CSRFProtection.verifyToken(token, sessionToken, secret)) {
        return NextResponse.json(
          {
            success: false,
            error: 'CSRF token invalid',
            code: 'CSRF_ERROR',
          },
          { status: 403 }
        );
      }
      
      return null; // Allow request
    };
  }
  
  /**
   * Content type validation
   */
  static validateContentType(allowedTypes: string[] = ['application/json']) {
    return (request: Request): NextResponse | null => {
      if (['GET', 'HEAD', 'OPTIONS'].includes(request.method)) {
        return null;
      }
      
      const contentType = request.headers.get('content-type');
      if (!contentType || !allowedTypes.some(type => contentType.includes(type))) {
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid content type',
            code: 'INVALID_CONTENT_TYPE',
          },
          { status: 400 }
        );
      }
      
      return null;
    };
  }
  
  /**
   * Request size validation
   */
  static validateRequestSize(maxSize: number = 1024 * 1024) { // 1MB default
    return async (request: Request): Promise<NextResponse | null> => {
      const contentLength = request.headers.get('content-length');
      if (contentLength && parseInt(contentLength) > maxSize) {
        return NextResponse.json(
          {
            success: false,
            error: 'Request too large',
            code: 'REQUEST_TOO_LARGE',
          },
          { status: 413 }
        );
      }
      
      return null;
    };
  }
  
  /**
   * Origin validation for CORS
   */
  static validateOrigin(allowedOrigins: string[] = []) {
    return (request: Request): NextResponse | null => {
      const origin = request.headers.get('origin');
      
      // Allow same-origin requests
      if (!origin) return null;
      
      // Check against allowed origins
      if (allowedOrigins.length > 0 && !allowedOrigins.includes(origin)) {
        return NextResponse.json(
          {
            success: false,
            error: 'Origin not allowed',
            code: 'ORIGIN_NOT_ALLOWED',
          },
          { status: 403 }
        );
      }
      
      return null;
    };
  }
  
  /**
   * User agent validation
   */
  static validateUserAgent(blockedPatterns: RegExp[] = []) {
    return (request: Request): NextResponse | null => {
      const userAgent = request.headers.get('user-agent');
      
      if (!userAgent) {
        return NextResponse.json(
          {
            success: false,
            error: 'User agent required',
            code: 'USER_AGENT_REQUIRED',
          },
          { status: 400 }
        );
      }
      
      // Check against blocked patterns
      for (const pattern of blockedPatterns) {
        if (pattern.test(userAgent)) {
          return NextResponse.json(
            {
              success: false,
              error: 'User agent blocked',
              code: 'USER_AGENT_BLOCKED',
            },
            { status: 403 }
          );
        }
      }
      
      return null;
    };
  }
}

// Security utilities
export class SecurityUtils {
  /**
   * Generate nonce for CSP
   */
  static generateNonce(): string {
    return crypto.randomBytes(16).toString('base64');
  }
  
  /**
   * Create secure cookie options
   */
  static getSecureCookieOptions(maxAge?: number) {
    return {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'strict' as const,
      maxAge: maxAge || 60 * 60 * 24 * 7, // 7 days default
      path: '/',
    };
  }
  
  /**
   * Sanitize filename for uploads
   */
  static sanitizeFilename(filename: string): string {
    return filename
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .replace(/_{2,}/g, '_')
      .substring(0, 255);
  }
  
  /**
   * Check if IP is in allowed range
   */
  static isIPAllowed(ip: string, allowedRanges: string[] = []): boolean {
    if (allowedRanges.length === 0) return true;
    
    // Simple IP range checking (for production, use a proper IP range library)
    return allowedRanges.some(range => {
      if (range.includes('/')) {
        // CIDR notation - simplified check
        const [network, prefix] = range.split('/');
        return ip.startsWith(network.split('.').slice(0, parseInt(prefix) / 8).join('.'));
      } else {
        // Exact match or wildcard
        return range === ip || range === '*';
      }
    });
  }
  
  /**
   * Generate secure session ID
   */
  static generateSessionId(): string {
    return crypto.randomBytes(32).toString('hex');
  }
  
  /**
   * Hash sensitive data for logging
   */
  static hashForLogging(data: string): string {
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 8);
  }
}
