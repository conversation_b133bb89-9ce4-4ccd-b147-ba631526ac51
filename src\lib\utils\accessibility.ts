/**
 * Accessibility Utilities
 * Provides comprehensive accessibility features and ARIA support
 */

import { logger } from './logging';

// Accessibility preferences
export interface AccessibilityPreferences {
  reducedMotion: boolean;
  highContrast: boolean;
  largeText: boolean;
  screenReader: boolean;
  keyboardNavigation: boolean;
  focusIndicators: boolean;
  announcements: boolean;
}

// ARIA live region types
export type AriaLiveType = 'polite' | 'assertive' | 'off';

// Focus management
export interface FocusOptions {
  preventScroll?: boolean;
  restoreFocus?: boolean;
  trapFocus?: boolean;
}

class AccessibilityManager {
  private preferences: AccessibilityPreferences;
  private liveRegions: Map<string, HTMLElement> = new Map();
  private focusStack: HTMLElement[] = [];
  private trapStack: HTMLElement[] = [];

  constructor() {
    this.preferences = this.detectPreferences();
    this.initializeLiveRegions();
    this.setupEventListeners();
  }

  // Detect user accessibility preferences
  private detectPreferences(): AccessibilityPreferences {
    const preferences: AccessibilityPreferences = {
      reducedMotion: false,
      highContrast: false,
      largeText: false,
      screenReader: false,
      keyboardNavigation: false,
      focusIndicators: true,
      announcements: true,
    };

    if (typeof window !== 'undefined') {
      // Detect reduced motion preference
      const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
      preferences.reducedMotion = reducedMotionQuery.matches;

      // Detect high contrast preference
      const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
      preferences.highContrast = highContrastQuery.matches;

      // Detect screen reader
      preferences.screenReader = this.detectScreenReader();

      // Load saved preferences
      const saved = this.loadPreferences();
      Object.assign(preferences, saved);
    }

    return preferences;
  }

  // Detect screen reader usage
  private detectScreenReader(): boolean {
    if (typeof window === 'undefined') return false;

    // Check for common screen reader indicators
    const indicators = [
      'speechSynthesis' in window,
      navigator.userAgent.includes('NVDA'),
      navigator.userAgent.includes('JAWS'),
      navigator.userAgent.includes('VoiceOver'),
    ];

    return indicators.some(Boolean);
  }

  // Initialize ARIA live regions
  private initializeLiveRegions(): void {
    if (typeof document === 'undefined') return;

    // Create polite live region
    const politeRegion = document.createElement('div');
    politeRegion.setAttribute('aria-live', 'polite');
    politeRegion.setAttribute('aria-atomic', 'true');
    politeRegion.className = 'sr-only';
    politeRegion.id = 'aria-live-polite';
    document.body.appendChild(politeRegion);
    this.liveRegions.set('polite', politeRegion);

    // Create assertive live region
    const assertiveRegion = document.createElement('div');
    assertiveRegion.setAttribute('aria-live', 'assertive');
    assertiveRegion.setAttribute('aria-atomic', 'true');
    assertiveRegion.className = 'sr-only';
    assertiveRegion.id = 'aria-live-assertive';
    document.body.appendChild(assertiveRegion);
    this.liveRegions.set('assertive', assertiveRegion);

    logger.debug('ARIA live regions initialized');
  }

  // Setup event listeners
  private setupEventListeners(): void {
    if (typeof window === 'undefined') return;

    // Listen for preference changes
    const reducedMotionQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    reducedMotionQuery.addEventListener('change', (e) => {
      this.preferences.reducedMotion = e.matches;
      this.applyPreferences();
    });

    const highContrastQuery = window.matchMedia('(prefers-contrast: high)');
    highContrastQuery.addEventListener('change', (e) => {
      this.preferences.highContrast = e.matches;
      this.applyPreferences();
    });

    // Keyboard navigation detection
    document.addEventListener('keydown', (e) => {
      if (e.key === 'Tab') {
        this.preferences.keyboardNavigation = true;
        document.body.classList.add('keyboard-navigation');
      }
    });

    document.addEventListener('mousedown', () => {
      document.body.classList.remove('keyboard-navigation');
    });
  }

  // Apply accessibility preferences
  private applyPreferences(): void {
    if (typeof document === 'undefined') return;

    const root = document.documentElement;

    // Apply reduced motion
    if (this.preferences.reducedMotion) {
      root.classList.add('reduce-motion');
    } else {
      root.classList.remove('reduce-motion');
    }

    // Apply high contrast
    if (this.preferences.highContrast) {
      root.classList.add('high-contrast');
    } else {
      root.classList.remove('high-contrast');
    }

    // Apply large text
    if (this.preferences.largeText) {
      root.classList.add('large-text');
    } else {
      root.classList.remove('large-text');
    }

    // Apply focus indicators
    if (this.preferences.focusIndicators) {
      root.classList.add('focus-indicators');
    } else {
      root.classList.remove('focus-indicators');
    }

    logger.info('Accessibility preferences applied', this.preferences);
  }

  // Announce message to screen readers
  announce(message: string, priority: AriaLiveType = 'polite'): void {
    if (!this.preferences.announcements) return;

    const region = this.liveRegions.get(priority);
    if (region) {
      // Clear previous message
      region.textContent = '';
      
      // Set new message after a brief delay to ensure it's announced
      setTimeout(() => {
        region.textContent = message;
      }, 100);

      logger.debug('Screen reader announcement', { message, priority });
    }
  }

  // Focus management
  focus(element: HTMLElement, options: FocusOptions = {}): void {
    if (!element) return;

    // Store current focus for restoration
    if (options.restoreFocus && document.activeElement instanceof HTMLElement) {
      this.focusStack.push(document.activeElement);
    }

    // Focus the element
    element.focus({ preventScroll: options.preventScroll });

    // Setup focus trap if requested
    if (options.trapFocus) {
      this.trapFocus(element);
    }

    logger.debug('Focus set', { element: element.tagName, options });
  }

  // Restore previous focus
  restoreFocus(): void {
    const previousElement = this.focusStack.pop();
    if (previousElement && document.contains(previousElement)) {
      previousElement.focus();
      logger.debug('Focus restored', { element: previousElement.tagName });
    }
  }

  // Trap focus within an element
  trapFocus(container: HTMLElement): void {
    const focusableElements = this.getFocusableElements(container);
    if (focusableElements.length === 0) return;

    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];

    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key !== 'Tab') return;

      if (e.shiftKey) {
        // Shift + Tab
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement.focus();
        }
      } else {
        // Tab
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement.focus();
        }
      }
    };

    container.addEventListener('keydown', handleKeyDown);
    this.trapStack.push(container);

    // Focus first element
    firstElement.focus();

    logger.debug('Focus trap activated', { container: container.tagName });
  }

  // Release focus trap
  releaseFocusTrap(): void {
    const container = this.trapStack.pop();
    if (container) {
      container.removeEventListener('keydown', this.handleTrapKeyDown);
      logger.debug('Focus trap released', { container: container.tagName });
    }
  }

  // Get focusable elements within a container
  private getFocusableElements(container: HTMLElement): HTMLElement[] {
    const focusableSelectors = [
      'a[href]',
      'button:not([disabled])',
      'input:not([disabled])',
      'select:not([disabled])',
      'textarea:not([disabled])',
      '[tabindex]:not([tabindex="-1"])',
      '[contenteditable="true"]',
    ].join(', ');

    return Array.from(container.querySelectorAll(focusableSelectors))
      .filter((element) => {
        return element instanceof HTMLElement && this.isVisible(element);
      }) as HTMLElement[];
  }

  // Check if element is visible
  private isVisible(element: HTMLElement): boolean {
    const style = window.getComputedStyle(element);
    return (
      style.display !== 'none' &&
      style.visibility !== 'hidden' &&
      style.opacity !== '0' &&
      element.offsetWidth > 0 &&
      element.offsetHeight > 0
    );
  }

  // Handle trap keydown (bound method)
  private handleTrapKeyDown = (e: KeyboardEvent) => {
    // Implementation moved to trapFocus method
  };

  // Keyboard navigation helpers
  handleArrowNavigation(
    event: KeyboardEvent,
    items: HTMLElement[],
    currentIndex: number,
    options: {
      wrap?: boolean;
      orientation?: 'horizontal' | 'vertical' | 'both';
    } = {}
  ): number {
    const { wrap = true, orientation = 'both' } = options;
    let newIndex = currentIndex;

    switch (event.key) {
      case 'ArrowUp':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          newIndex = wrap && currentIndex === 0 ? items.length - 1 : Math.max(0, currentIndex - 1);
        }
        break;
      case 'ArrowDown':
        if (orientation === 'vertical' || orientation === 'both') {
          event.preventDefault();
          newIndex = wrap && currentIndex === items.length - 1 ? 0 : Math.min(items.length - 1, currentIndex + 1);
        }
        break;
      case 'ArrowLeft':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          newIndex = wrap && currentIndex === 0 ? items.length - 1 : Math.max(0, currentIndex - 1);
        }
        break;
      case 'ArrowRight':
        if (orientation === 'horizontal' || orientation === 'both') {
          event.preventDefault();
          newIndex = wrap && currentIndex === items.length - 1 ? 0 : Math.min(items.length - 1, currentIndex + 1);
        }
        break;
      case 'Home':
        event.preventDefault();
        newIndex = 0;
        break;
      case 'End':
        event.preventDefault();
        newIndex = items.length - 1;
        break;
    }

    if (newIndex !== currentIndex && items[newIndex]) {
      items[newIndex].focus();
    }

    return newIndex;
  }

  // Generate unique IDs for ARIA relationships
  generateId(prefix: string = 'aria'): string {
    return `${prefix}-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  // Preferences management
  updatePreferences(updates: Partial<AccessibilityPreferences>): void {
    this.preferences = { ...this.preferences, ...updates };
    this.applyPreferences();
    this.savePreferences();
    
    logger.info('Accessibility preferences updated', updates);
  }

  getPreferences(): AccessibilityPreferences {
    return { ...this.preferences };
  }

  private loadPreferences(): Partial<AccessibilityPreferences> {
    try {
      const stored = localStorage.getItem('accessibility-preferences');
      return stored ? JSON.parse(stored) : {};
    } catch (error) {
      logger.warn('Failed to load accessibility preferences', { error });
      return {};
    }
  }

  private savePreferences(): void {
    try {
      localStorage.setItem('accessibility-preferences', JSON.stringify(this.preferences));
    } catch (error) {
      logger.warn('Failed to save accessibility preferences', { error });
    }
  }
}

// Create singleton instance
export const accessibilityManager = new AccessibilityManager();

// React hook for accessibility features
export function useAccessibility() {
  const [preferences, setPreferences] = React.useState(accessibilityManager.getPreferences());

  const updatePreferences = React.useCallback((updates: Partial<AccessibilityPreferences>) => {
    accessibilityManager.updatePreferences(updates);
    setPreferences(accessibilityManager.getPreferences());
  }, []);

  const announce = React.useCallback((message: string, priority?: AriaLiveType) => {
    accessibilityManager.announce(message, priority);
  }, []);

  const focus = React.useCallback((element: HTMLElement, options?: FocusOptions) => {
    accessibilityManager.focus(element, options);
  }, []);

  return {
    preferences,
    updatePreferences,
    announce,
    focus,
    restoreFocus: accessibilityManager.restoreFocus.bind(accessibilityManager),
    trapFocus: accessibilityManager.trapFocus.bind(accessibilityManager),
    releaseFocusTrap: accessibilityManager.releaseFocusTrap.bind(accessibilityManager),
    handleArrowNavigation: accessibilityManager.handleArrowNavigation.bind(accessibilityManager),
    generateId: accessibilityManager.generateId.bind(accessibilityManager),
  };
}

export default accessibilityManager;
