/**
 * Enhanced Validation Utilities
 * Provides comprehensive validation functions for all application data
 */

import { createError, ErrorCodes } from './error-handling';

// Validation result interface
export interface ValidationResult {
  isValid: boolean;
  errors: ValidationError[];
  warnings: ValidationWarning[];
}

export interface ValidationError {
  field: string;
  message: string;
  code: string;
  value?: unknown;
}

export interface ValidationWarning {
  field: string;
  message: string;
  code: string;
  value?: unknown;
}

// Validation rule types
export type ValidationRule<T = any> = {
  required?: boolean;
  min?: number;
  max?: number;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: T) => boolean | string;
  type?: 'string' | 'number' | 'boolean' | 'array' | 'object' | 'email' | 'url' | 'ip' | 'coordinate';
};

export type ValidationSchema<T> = {
  [K in keyof T]?: ValidationRule<T[K]>;
};

// Core validation functions
export function validateValue<T>(
  value: T,
  rules: ValidationRule<T>,
  fieldName: string = 'field'
): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  // Required check
  if (rules.required && (value === null || value === undefined || value === '')) {
    errors.push({
      field: fieldName,
      message: `${fieldName} is required`,
      code: 'REQUIRED',
      value,
    });
    return { isValid: false, errors, warnings };
  }

  // Skip other validations if value is empty and not required
  if (!rules.required && (value === null || value === undefined || value === '')) {
    return { isValid: true, errors, warnings };
  }

  // Type validation
  if (rules.type) {
    const typeError = validateType(value, rules.type, fieldName);
    if (typeError) {
      errors.push(typeError);
    }
  }

  // String validations
  if (typeof value === 'string') {
    if (rules.minLength !== undefined && value.length < rules.minLength) {
      errors.push({
        field: fieldName,
        message: `${fieldName} must be at least ${rules.minLength} characters`,
        code: 'MIN_LENGTH',
        value,
      });
    }

    if (rules.maxLength !== undefined && value.length > rules.maxLength) {
      errors.push({
        field: fieldName,
        message: `${fieldName} must be no more than ${rules.maxLength} characters`,
        code: 'MAX_LENGTH',
        value,
      });
    }

    if (rules.pattern && !rules.pattern.test(value)) {
      errors.push({
        field: fieldName,
        message: `${fieldName} format is invalid`,
        code: 'PATTERN',
        value,
      });
    }
  }

  // Number validations
  if (typeof value === 'number') {
    if (rules.min !== undefined && value < rules.min) {
      errors.push({
        field: fieldName,
        message: `${fieldName} must be at least ${rules.min}`,
        code: 'MIN_VALUE',
        value,
      });
    }

    if (rules.max !== undefined && value > rules.max) {
      errors.push({
        field: fieldName,
        message: `${fieldName} must be no more than ${rules.max}`,
        code: 'MAX_VALUE',
        value,
      });
    }

    if (isNaN(value)) {
      errors.push({
        field: fieldName,
        message: `${fieldName} must be a valid number`,
        code: 'INVALID_NUMBER',
        value,
      });
    }
  }

  // Array validations
  if (Array.isArray(value)) {
    if (rules.minLength !== undefined && value.length < rules.minLength) {
      errors.push({
        field: fieldName,
        message: `${fieldName} must have at least ${rules.minLength} items`,
        code: 'MIN_ITEMS',
        value,
      });
    }

    if (rules.maxLength !== undefined && value.length > rules.maxLength) {
      errors.push({
        field: fieldName,
        message: `${fieldName} must have no more than ${rules.maxLength} items`,
        code: 'MAX_ITEMS',
        value,
      });
    }
  }

  // Custom validation
  if (rules.custom) {
    const customResult = rules.custom(value);
    if (customResult !== true) {
      errors.push({
        field: fieldName,
        message: typeof customResult === 'string' ? customResult : `${fieldName} is invalid`,
        code: 'CUSTOM',
        value,
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// Validate type
function validateType(value: unknown, type: string, fieldName: string): ValidationError | null {
  switch (type) {
    case 'string':
      if (typeof value !== 'string') {
        return {
          field: fieldName,
          message: `${fieldName} must be a string`,
          code: 'INVALID_TYPE',
          value,
        };
      }
      break;

    case 'number':
      if (typeof value !== 'number' || isNaN(value)) {
        return {
          field: fieldName,
          message: `${fieldName} must be a number`,
          code: 'INVALID_TYPE',
          value,
        };
      }
      break;

    case 'boolean':
      if (typeof value !== 'boolean') {
        return {
          field: fieldName,
          message: `${fieldName} must be a boolean`,
          code: 'INVALID_TYPE',
          value,
        };
      }
      break;

    case 'array':
      if (!Array.isArray(value)) {
        return {
          field: fieldName,
          message: `${fieldName} must be an array`,
          code: 'INVALID_TYPE',
          value,
        };
      }
      break;

    case 'object':
      if (typeof value !== 'object' || value === null || Array.isArray(value)) {
        return {
          field: fieldName,
          message: `${fieldName} must be an object`,
          code: 'INVALID_TYPE',
          value,
        };
      }
      break;

    case 'email':
      if (typeof value !== 'string' || !isValidEmail(value)) {
        return {
          field: fieldName,
          message: `${fieldName} must be a valid email address`,
          code: 'INVALID_EMAIL',
          value,
        };
      }
      break;

    case 'url':
      if (typeof value !== 'string' || !isValidUrl(value)) {
        return {
          field: fieldName,
          message: `${fieldName} must be a valid URL`,
          code: 'INVALID_URL',
          value,
        };
      }
      break;

    case 'ip':
      if (typeof value !== 'string' || !isValidIP(value)) {
        return {
          field: fieldName,
          message: `${fieldName} must be a valid IP address`,
          code: 'INVALID_IP',
          value,
        };
      }
      break;

    case 'coordinate':
      if (typeof value !== 'string' || !isValidCoordinate(value)) {
        return {
          field: fieldName,
          message: `${fieldName} must be a valid coordinate`,
          code: 'INVALID_COORDINATE',
          value,
        };
      }
      break;
  }

  return null;
}

// Validate object against schema
export function validateObject<T extends Record<string, any>>(
  obj: T,
  schema: ValidationSchema<T>
): ValidationResult {
  const errors: ValidationError[] = [];
  const warnings: ValidationWarning[] = [];

  for (const [key, rules] of Object.entries(schema)) {
    if (rules) {
      const result = validateValue(obj[key], rules, key);
      errors.push(...result.errors);
      warnings.push(...result.warnings);
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
    warnings,
  };
}

// Specific validation functions
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
}

export function isValidUrl(url: string): boolean {
  try {
    new URL(url);
    return true;
  } catch {
    return false;
  }
}

export function isValidIP(ip: string): boolean {
  const ipv4Regex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
  const ipv6Regex = /^(?:[0-9a-fA-F]{1,4}:){7}[0-9a-fA-F]{1,4}$/;
  return ipv4Regex.test(ip) || ipv6Regex.test(ip);
}

export function isValidCoordinate(coord: string): boolean {
  // RA format: HH:MM:SS or HH MM SS
  const raRegex = /^([0-1]?[0-9]|2[0-3])[:\s]([0-5]?[0-9])[:\s]([0-5]?[0-9](?:\.[0-9]+)?)$/;
  // Dec format: ±DD:MM:SS or ±DD MM SS
  const decRegex = /^[+-]?([0-8]?[0-9]|90)[:\s]([0-5]?[0-9])[:\s]([0-5]?[0-9](?:\.[0-9]+)?)$/;
  
  return raRegex.test(coord) || decRegex.test(coord);
}

// Equipment-specific validations
export function validateEquipmentSettings(settings: any): ValidationResult {
  const schema = {
    host: { required: true, type: 'string' as const, minLength: 1 },
    port: { required: true, type: 'number' as const, min: 1, max: 65535 },
    timeout: { type: 'number' as const, min: 1000, max: 30000 },
    retries: { type: 'number' as const, min: 0, max: 10 },
  };

  return validateObject(settings, schema);
}

export function validateCameraSettings(settings: any): ValidationResult {
  const schema = {
    exposureTime: { required: true, type: 'number' as const, min: 0.001, max: 3600 },
    gain: { type: 'number' as const, min: 0, max: 100 },
    binning: { type: 'number' as const, min: 1, max: 4 },
    temperature: { type: 'number' as const, min: -50, max: 50 },
  };

  return validateObject(settings, schema);
}

export function validateMountSettings(settings: any): ValidationResult {
  const schema = {
    ra: { required: true, type: 'coordinate' as const },
    dec: { required: true, type: 'coordinate' as const },
    slewRate: { type: 'string' as const, pattern: /^(guide|centering|find|max)$/ },
    trackingRate: { type: 'string' as const, pattern: /^(sidereal|lunar|solar|king)$/ },
  };

  return validateObject(settings, schema);
}

// Validation error handler
export function handleValidationError(result: ValidationResult, context?: string): void {
  if (!result.isValid) {
    const errorMessage = result.errors.map(e => e.message).join(', ');
    const error = createError(`Validation failed: ${errorMessage}`, {
      code: ErrorCodes.VALIDATION_ERROR,
      category: 'validation',
      severity: 'medium',
      context: { validationErrors: result.errors, context },
    });
    
    throw error;
  }
}

// Async validation wrapper
export async function validateAsync<T>(
  value: T,
  validator: (value: T) => Promise<boolean | string>
): Promise<ValidationResult> {
  try {
    const result = await validator(value);
    
    if (result === true) {
      return { isValid: true, errors: [], warnings: [] };
    }
    
    return {
      isValid: false,
      errors: [{
        field: 'value',
        message: typeof result === 'string' ? result : 'Validation failed',
        code: 'ASYNC_VALIDATION',
        value,
      }],
      warnings: [],
    };
  } catch (error) {
    return {
      isValid: false,
      errors: [{
        field: 'value',
        message: error instanceof Error ? error.message : 'Async validation error',
        code: 'ASYNC_ERROR',
        value,
      }],
      warnings: [],
    };
  }
}
