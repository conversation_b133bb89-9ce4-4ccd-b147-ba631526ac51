/**
 * Mobile Optimization Utilities
 * Provides comprehensive mobile experience enhancements
 */

import { logger } from './logging';
import { performanceMonitor } from './performance-monitor';

// Device information
export interface DeviceInfo {
  type: 'mobile' | 'tablet' | 'desktop';
  os: 'ios' | 'android' | 'windows' | 'macos' | 'linux' | 'unknown';
  browser: 'chrome' | 'firefox' | 'safari' | 'edge' | 'unknown';
  screenSize: 'small' | 'medium' | 'large' | 'xlarge';
  orientation: 'portrait' | 'landscape';
  touchSupport: boolean;
  connectionType: 'slow' | 'fast' | 'unknown';
  batteryLevel?: number;
  isLowPowerMode?: boolean;
}

// Touch gesture types
export type GestureType = 'tap' | 'swipe' | 'pinch' | 'rotate' | 'long-press';

// Gesture event interface
export interface GestureEvent {
  type: GestureType;
  startX: number;
  startY: number;
  endX: number;
  endY: number;
  deltaX: number;
  deltaY: number;
  distance: number;
  duration: number;
  velocity: number;
  direction?: 'up' | 'down' | 'left' | 'right';
  scale?: number;
  rotation?: number;
}

// Gesture options
export interface GestureOptions {
  threshold: number;
  timeThreshold: number;
  preventDefault: boolean;
  stopPropagation: boolean;
}

// Viewport management
export interface ViewportState {
  width: number;
  height: number;
  scale: number;
  orientation: number;
  isKeyboardOpen: boolean;
}

class MobileOptimizationManager {
  private deviceInfo: DeviceInfo;
  private gestureHandlers: Map<HTMLElement, Map<GestureType, Function>> = new Map();
  private viewportState: ViewportState;
  private orientationChangeCallbacks: Set<(orientation: 'portrait' | 'landscape') => void> = new Set();

  constructor() {
    this.deviceInfo = this.detectDevice();
    this.viewportState = this.getViewportState();
    this.initializeOptimizations();
    this.setupEventListeners();
  }

  // Detect device capabilities and characteristics
  private detectDevice(): DeviceInfo {
    const userAgent = navigator.userAgent.toLowerCase();
    
    // Detect device type
    let type: DeviceInfo['type'] = 'desktop';
    if (/mobile|android|iphone|ipod|blackberry|iemobile|opera mini/i.test(userAgent)) {
      type = 'mobile';
    } else if (/tablet|ipad|playbook|silk/i.test(userAgent)) {
      type = 'tablet';
    }

    // Detect OS
    let os: DeviceInfo['os'] = 'unknown';
    if (/iphone|ipad|ipod/i.test(userAgent)) {
      os = 'ios';
    } else if (/android/i.test(userAgent)) {
      os = 'android';
    } else if (/windows/i.test(userAgent)) {
      os = 'windows';
    } else if (/mac/i.test(userAgent)) {
      os = 'macos';
    } else if (/linux/i.test(userAgent)) {
      os = 'linux';
    }

    // Detect browser
    let browser: DeviceInfo['browser'] = 'unknown';
    if (/chrome/i.test(userAgent)) {
      browser = 'chrome';
    } else if (/firefox/i.test(userAgent)) {
      browser = 'firefox';
    } else if (/safari/i.test(userAgent)) {
      browser = 'safari';
    } else if (/edge/i.test(userAgent)) {
      browser = 'edge';
    }

    // Detect screen size
    const width = window.innerWidth;
    let screenSize: DeviceInfo['screenSize'] = 'medium';
    if (width < 640) {
      screenSize = 'small';
    } else if (width < 1024) {
      screenSize = 'medium';
    } else if (width < 1280) {
      screenSize = 'large';
    } else {
      screenSize = 'xlarge';
    }

    // Detect orientation
    const orientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';

    // Detect touch support
    const touchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0;

    // Detect connection type
    let connectionType: DeviceInfo['connectionType'] = 'unknown';
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g') {
        connectionType = 'slow';
      } else {
        connectionType = 'fast';
      }
    }

    // Detect battery information
    let batteryLevel: number | undefined;
    let isLowPowerMode: boolean | undefined;
    if ('getBattery' in navigator) {
      (navigator as any).getBattery().then((battery: any) => {
        batteryLevel = battery.level;
        isLowPowerMode = battery.level < 0.2;
      });
    }

    return {
      type,
      os,
      browser,
      screenSize,
      orientation,
      touchSupport,
      connectionType,
      batteryLevel,
      isLowPowerMode,
    };
  }

  // Get current viewport state
  private getViewportState(): ViewportState {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      scale: window.devicePixelRatio || 1,
      orientation: window.orientation || 0,
      isKeyboardOpen: this.detectKeyboard(),
    };
  }

  // Detect if virtual keyboard is open
  private detectKeyboard(): boolean {
    if (this.deviceInfo.type === 'desktop') return false;
    
    const heightRatio = window.innerHeight / window.screen.height;
    return heightRatio < 0.75; // Heuristic for keyboard detection
  }

  // Initialize mobile optimizations
  private initializeOptimizations(): void {
    if (typeof document === 'undefined') return;

    // Prevent zoom on input focus (iOS)
    if (this.deviceInfo.os === 'ios') {
      const viewport = document.querySelector('meta[name="viewport"]');
      if (viewport) {
        viewport.setAttribute('content', 
          'width=device-width, initial-scale=1, maximum-scale=1, user-scalable=no'
        );
      }
    }

    // Add device classes to body
    document.body.classList.add(
      `device-${this.deviceInfo.type}`,
      `os-${this.deviceInfo.os}`,
      `browser-${this.deviceInfo.browser}`,
      `screen-${this.deviceInfo.screenSize}`,
      `orientation-${this.deviceInfo.orientation}`
    );

    if (this.deviceInfo.touchSupport) {
      document.body.classList.add('touch-device');
    }

    // Optimize for low power mode
    if (this.deviceInfo.isLowPowerMode) {
      this.enableLowPowerMode();
    }

    logger.info('Mobile optimizations initialized', this.deviceInfo);
  }

  // Setup event listeners
  private setupEventListeners(): void {
    // Orientation change
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.handleOrientationChange();
      }, 100); // Delay to ensure viewport has updated
    });

    // Resize events
    window.addEventListener('resize', () => {
      this.handleResize();
    });

    // Visibility change (for battery optimization)
    document.addEventListener('visibilitychange', () => {
      this.handleVisibilityChange();
    });

    // Network change
    if ('connection' in navigator) {
      (navigator as any).connection.addEventListener('change', () => {
        this.handleNetworkChange();
      });
    }
  }

  // Handle orientation change
  private handleOrientationChange(): void {
    const newOrientation = window.innerHeight > window.innerWidth ? 'portrait' : 'landscape';
    
    if (newOrientation !== this.deviceInfo.orientation) {
      this.deviceInfo.orientation = newOrientation;
      this.viewportState = this.getViewportState();
      
      // Update body classes
      document.body.classList.remove('orientation-portrait', 'orientation-landscape');
      document.body.classList.add(`orientation-${newOrientation}`);
      
      // Notify callbacks
      this.orientationChangeCallbacks.forEach(callback => {
        try {
          callback(newOrientation);
        } catch (error) {
          logger.error('Orientation change callback error', { error });
        }
      });
      
      logger.info('Orientation changed', { orientation: newOrientation });
    }
  }

  // Handle resize events
  private handleResize(): void {
    const oldState = this.viewportState;
    this.viewportState = this.getViewportState();
    
    // Detect keyboard state change
    if (oldState.isKeyboardOpen !== this.viewportState.isKeyboardOpen) {
      document.body.classList.toggle('keyboard-open', this.viewportState.isKeyboardOpen);
      
      logger.debug('Keyboard state changed', { 
        isOpen: this.viewportState.isKeyboardOpen 
      });
    }
  }

  // Handle visibility change
  private handleVisibilityChange(): void {
    if (document.hidden) {
      // App is in background, reduce activity
      this.enableBackgroundMode();
    } else {
      // App is in foreground, resume normal activity
      this.disableBackgroundMode();
    }
  }

  // Handle network change
  private handleNetworkChange(): void {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection;
      const oldType = this.deviceInfo.connectionType;
      
      if (connection.effectiveType === '2g' || connection.effectiveType === 'slow-2g') {
        this.deviceInfo.connectionType = 'slow';
      } else {
        this.deviceInfo.connectionType = 'fast';
      }
      
      if (oldType !== this.deviceInfo.connectionType) {
        logger.info('Network type changed', { 
          from: oldType, 
          to: this.deviceInfo.connectionType 
        });
        
        // Adjust optimizations based on connection
        if (this.deviceInfo.connectionType === 'slow') {
          this.enableSlowConnectionMode();
        } else {
          this.disableSlowConnectionMode();
        }
      }
    }
  }

  // Gesture recognition
  addGestureListener(
    element: HTMLElement,
    gestureType: GestureType,
    handler: (event: GestureEvent) => void,
    options: Partial<GestureOptions> = {}
  ): () => void {
    const defaultOptions: GestureOptions = {
      threshold: 10,
      timeThreshold: 500,
      preventDefault: true,
      stopPropagation: false,
    };
    
    const finalOptions = { ...defaultOptions, ...options };
    
    if (!this.gestureHandlers.has(element)) {
      this.gestureHandlers.set(element, new Map());
    }
    
    const elementHandlers = this.gestureHandlers.get(element)!;
    elementHandlers.set(gestureType, handler);
    
    // Setup touch event listeners
    this.setupGestureListeners(element, finalOptions);
    
    // Return cleanup function
    return () => {
      elementHandlers.delete(gestureType);
      if (elementHandlers.size === 0) {
        this.gestureHandlers.delete(element);
        this.removeGestureListeners(element);
      }
    };
  }

  // Setup gesture listeners for an element
  private setupGestureListeners(element: HTMLElement, options: GestureOptions): void {
    let startTouch: Touch | null = null;
    let startTime: number = 0;
    
    const handleTouchStart = (e: TouchEvent) => {
      if (e.touches.length === 1) {
        startTouch = e.touches[0];
        startTime = Date.now();
        
        if (options.preventDefault) {
          e.preventDefault();
        }
        if (options.stopPropagation) {
          e.stopPropagation();
        }
      }
    };
    
    const handleTouchEnd = (e: TouchEvent) => {
      if (startTouch && e.changedTouches.length === 1) {
        const endTouch = e.changedTouches[0];
        const endTime = Date.now();
        const duration = endTime - startTime;
        
        const deltaX = endTouch.clientX - startTouch.clientX;
        const deltaY = endTouch.clientY - startTouch.clientY;
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY);
        const velocity = distance / duration;
        
        // Determine gesture type
        let gestureType: GestureType;
        let direction: 'up' | 'down' | 'left' | 'right' | undefined;
        
        if (distance < options.threshold && duration < options.timeThreshold) {
          gestureType = 'tap';
        } else if (duration > 500 && distance < options.threshold) {
          gestureType = 'long-press';
        } else if (distance >= options.threshold) {
          gestureType = 'swipe';
          
          // Determine direction
          if (Math.abs(deltaX) > Math.abs(deltaY)) {
            direction = deltaX > 0 ? 'right' : 'left';
          } else {
            direction = deltaY > 0 ? 'down' : 'up';
          }
        } else {
          return; // No recognized gesture
        }
        
        // Create gesture event
        const gestureEvent: GestureEvent = {
          type: gestureType,
          startX: startTouch.clientX,
          startY: startTouch.clientY,
          endX: endTouch.clientX,
          endY: endTouch.clientY,
          deltaX,
          deltaY,
          distance,
          duration,
          velocity,
          direction,
        };
        
        // Call handler if registered
        const elementHandlers = this.gestureHandlers.get(element);
        const handler = elementHandlers?.get(gestureType);
        if (handler) {
          try {
            handler(gestureEvent);
          } catch (error) {
            logger.error('Gesture handler error', { gestureType, error });
          }
        }
        
        startTouch = null;
      }
    };
    
    element.addEventListener('touchstart', handleTouchStart, { passive: !options.preventDefault });
    element.addEventListener('touchend', handleTouchEnd, { passive: !options.preventDefault });
  }

  // Remove gesture listeners
  private removeGestureListeners(element: HTMLElement): void {
    // Remove all touch event listeners
    element.removeEventListener('touchstart', () => {});
    element.removeEventListener('touchend', () => {});
  }

  // Performance optimizations
  private enableLowPowerMode(): void {
    document.body.classList.add('low-power-mode');
    
    // Reduce animations
    document.body.style.setProperty('--animation-duration', '0s');
    
    // Reduce update frequency
    performanceMonitor.recordMetric({
      name: 'low-power-mode-enabled',
      value: 1,
      unit: 'boolean',
      category: 'performance',
    });
    
    logger.info('Low power mode enabled');
  }

  private enableBackgroundMode(): void {
    // Pause non-critical operations
    logger.debug('Background mode enabled');
  }

  private disableBackgroundMode(): void {
    // Resume normal operations
    logger.debug('Background mode disabled');
  }

  private enableSlowConnectionMode(): void {
    document.body.classList.add('slow-connection');
    logger.info('Slow connection mode enabled');
  }

  private disableSlowConnectionMode(): void {
    document.body.classList.remove('slow-connection');
    logger.info('Slow connection mode disabled');
  }

  // Public API
  getDeviceInfo(): DeviceInfo {
    return { ...this.deviceInfo };
  }

  getViewportState(): ViewportState {
    return { ...this.viewportState };
  }

  onOrientationChange(callback: (orientation: 'portrait' | 'landscape') => void): () => void {
    this.orientationChangeCallbacks.add(callback);
    return () => this.orientationChangeCallbacks.delete(callback);
  }

  // Haptic feedback
  vibrate(pattern: number | number[]): void {
    if ('vibrate' in navigator && this.deviceInfo.touchSupport) {
      try {
        navigator.vibrate(pattern);
      } catch (error) {
        logger.warn('Vibration failed', { error });
      }
    }
  }

  // Safe area utilities
  getSafeAreaInsets(): { top: number; right: number; bottom: number; left: number } {
    const style = getComputedStyle(document.documentElement);
    
    return {
      top: parseInt(style.getPropertyValue('--safe-area-inset-top') || '0'),
      right: parseInt(style.getPropertyValue('--safe-area-inset-right') || '0'),
      bottom: parseInt(style.getPropertyValue('--safe-area-inset-bottom') || '0'),
      left: parseInt(style.getPropertyValue('--safe-area-inset-left') || '0'),
    };
  }
}

// Create singleton instance
export const mobileOptimization = new MobileOptimizationManager();

// React hook for mobile optimization
export function useMobileOptimization() {
  const [deviceInfo, setDeviceInfo] = React.useState(mobileOptimization.getDeviceInfo());
  const [viewportState, setViewportState] = React.useState(mobileOptimization.getViewportState());

  React.useEffect(() => {
    const unsubscribe = mobileOptimization.onOrientationChange(() => {
      setDeviceInfo(mobileOptimization.getDeviceInfo());
      setViewportState(mobileOptimization.getViewportState());
    });

    return unsubscribe;
  }, []);

  const addGestureListener = React.useCallback(
    (element: HTMLElement, gestureType: GestureType, handler: (event: GestureEvent) => void, options?: Partial<GestureOptions>) => {
      return mobileOptimization.addGestureListener(element, gestureType, handler, options);
    },
    []
  );

  return {
    deviceInfo,
    viewportState,
    addGestureListener,
    vibrate: mobileOptimization.vibrate.bind(mobileOptimization),
    getSafeAreaInsets: mobileOptimization.getSafeAreaInsets.bind(mobileOptimization),
  };
}

export default mobileOptimization;
