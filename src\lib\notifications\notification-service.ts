/**
 * Enhanced Notification Service
 * Provides comprehensive notification management with multiple delivery channels
 */

import { logger } from '../utils/logging';
import { createError, ErrorCodes } from '../utils/error-handling';

// Notification types
export type NotificationType = 'info' | 'success' | 'warning' | 'error' | 'critical';

// Notification categories
export type NotificationCategory = 
  | 'system'
  | 'equipment'
  | 'sequence'
  | 'weather'
  | 'maintenance'
  | 'security'
  | 'user';

// Notification channels
export type NotificationChannel = 'toast' | 'push' | 'email' | 'sound' | 'vibration';

// Notification interface
export interface Notification {
  id: string;
  type: NotificationType;
  category: NotificationCategory;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
  persistent: boolean;
  channels: NotificationChannel[];
  actions?: NotificationAction[];
  metadata?: Record<string, unknown>;
  expiresAt?: Date;
  priority: number; // 1-10, higher is more important
}

export interface NotificationAction {
  id: string;
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'danger';
}

// Notification preferences
export interface NotificationPreferences {
  enabled: boolean;
  channels: {
    toast: boolean;
    push: boolean;
    email: boolean;
    sound: boolean;
    vibration: boolean;
  };
  categories: Record<NotificationCategory, boolean>;
  quietHours: {
    enabled: boolean;
    start: string; // HH:MM format
    end: string;   // HH:MM format
  };
  priority: {
    minimum: number; // Only show notifications with priority >= this value
  };
}

// Default preferences
const DEFAULT_PREFERENCES: NotificationPreferences = {
  enabled: true,
  channels: {
    toast: true,
    push: true,
    email: false,
    sound: true,
    vibration: true,
  },
  categories: {
    system: true,
    equipment: true,
    sequence: true,
    weather: false,
    maintenance: true,
    security: true,
    user: true,
  },
  quietHours: {
    enabled: false,
    start: '22:00',
    end: '08:00',
  },
  priority: {
    minimum: 3,
  },
};

class NotificationService {
  private notifications: Notification[] = [];
  private preferences: NotificationPreferences;
  private listeners: Set<(notification: Notification) => void> = new Set();
  private maxNotifications = 100;

  constructor() {
    this.preferences = this.loadPreferences();
    this.initializePushNotifications();
  }

  // Create and send notification
  async send(
    type: NotificationType,
    title: string,
    message: string,
    options: {
      category?: NotificationCategory;
      channels?: NotificationChannel[];
      persistent?: boolean;
      actions?: NotificationAction[];
      metadata?: Record<string, unknown>;
      priority?: number;
      expiresIn?: number; // milliseconds
    } = {}
  ): Promise<string> {
    const notification: Notification = {
      id: this.generateId(),
      type,
      category: options.category || 'system',
      title,
      message,
      timestamp: new Date(),
      read: false,
      persistent: options.persistent || false,
      channels: options.channels || this.getDefaultChannels(type),
      actions: options.actions,
      metadata: options.metadata,
      priority: options.priority || this.getDefaultPriority(type),
      expiresAt: options.expiresIn ? new Date(Date.now() + options.expiresIn) : undefined,
    };

    // Check if notification should be sent based on preferences
    if (!this.shouldSendNotification(notification)) {
      logger.debug('Notification filtered by preferences', {
        notificationId: notification.id,
        type,
        category: notification.category,
      });
      return notification.id;
    }

    // Add to notifications list
    this.notifications.unshift(notification);
    
    // Maintain max notifications limit
    if (this.notifications.length > this.maxNotifications) {
      this.notifications = this.notifications.slice(0, this.maxNotifications);
    }

    // Send through enabled channels
    await this.sendThroughChannels(notification);

    // Notify listeners
    this.notifyListeners(notification);

    logger.info('Notification sent', {
      notificationId: notification.id,
      type,
      category: notification.category,
      channels: notification.channels,
    });

    return notification.id;
  }

  // Convenience methods
  async info(title: string, message: string, options?: any): Promise<string> {
    return this.send('info', title, message, options);
  }

  async success(title: string, message: string, options?: any): Promise<string> {
    return this.send('success', title, message, options);
  }

  async warning(title: string, message: string, options?: any): Promise<string> {
    return this.send('warning', title, message, options);
  }

  async error(title: string, message: string, options?: any): Promise<string> {
    return this.send('error', title, message, options);
  }

  async critical(title: string, message: string, options?: any): Promise<string> {
    return this.send('critical', title, message, { ...options, persistent: true, priority: 10 });
  }

  // Send through notification channels
  private async sendThroughChannels(notification: Notification): Promise<void> {
    const promises = notification.channels.map(channel => {
      switch (channel) {
        case 'toast':
          return this.sendToastNotification(notification);
        case 'push':
          return this.sendPushNotification(notification);
        case 'email':
          return this.sendEmailNotification(notification);
        case 'sound':
          return this.playSoundNotification(notification);
        case 'vibration':
          return this.triggerVibration(notification);
        default:
          return Promise.resolve();
      }
    });

    try {
      await Promise.allSettled(promises);
    } catch (error) {
      logger.error('Failed to send notification through some channels', {
        notificationId: notification.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Toast notification (in-app)
  private async sendToastNotification(notification: Notification): Promise<void> {
    // This would integrate with your toast component
    // For now, we'll just log it
    logger.debug('Toast notification sent', {
      notificationId: notification.id,
      title: notification.title,
    });
  }

  // Push notification (browser/mobile)
  private async sendPushNotification(notification: Notification): Promise<void> {
    if (!('Notification' in window)) {
      logger.warn('Push notifications not supported');
      return;
    }

    if (Notification.permission !== 'granted') {
      logger.warn('Push notification permission not granted');
      return;
    }

    try {
      const pushNotification = new Notification(notification.title, {
        body: notification.message,
        icon: '/icon-192x192.png',
        badge: '/icon-72x72.png',
        tag: notification.id,
        requireInteraction: notification.persistent,
        data: notification.metadata,
      });

      pushNotification.onclick = () => {
        window.focus();
        pushNotification.close();
        this.markAsRead(notification.id);
      };

      logger.debug('Push notification sent', {
        notificationId: notification.id,
      });

    } catch (error) {
      logger.error('Failed to send push notification', {
        notificationId: notification.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Email notification
  private async sendEmailNotification(notification: Notification): Promise<void> {
    // This would integrate with your email service
    logger.debug('Email notification would be sent', {
      notificationId: notification.id,
      title: notification.title,
    });
  }

  // Sound notification
  private async playSoundNotification(notification: Notification): Promise<void> {
    try {
      const audio = new Audio('/sounds/notification.mp3');
      await audio.play();
      
      logger.debug('Sound notification played', {
        notificationId: notification.id,
      });
    } catch (error) {
      logger.warn('Failed to play sound notification', {
        notificationId: notification.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Vibration notification
  private async triggerVibration(notification: Notification): Promise<void> {
    if (!('vibrate' in navigator)) {
      logger.debug('Vibration not supported');
      return;
    }

    try {
      const pattern = this.getVibrationPattern(notification.type);
      navigator.vibrate(pattern);
      
      logger.debug('Vibration triggered', {
        notificationId: notification.id,
        pattern,
      });
    } catch (error) {
      logger.warn('Failed to trigger vibration', {
        notificationId: notification.id,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }
  }

  // Get vibration pattern based on notification type
  private getVibrationPattern(type: NotificationType): number[] {
    switch (type) {
      case 'critical':
        return [200, 100, 200, 100, 200];
      case 'error':
        return [300, 100, 300];
      case 'warning':
        return [200, 100, 200];
      case 'success':
        return [100];
      default:
        return [100];
    }
  }

  // Check if notification should be sent
  private shouldSendNotification(notification: Notification): boolean {
    // Check if notifications are enabled
    if (!this.preferences.enabled) {
      return false;
    }

    // Check category preferences
    if (!this.preferences.categories[notification.category]) {
      return false;
    }

    // Check priority threshold
    if (notification.priority < this.preferences.priority.minimum) {
      return false;
    }

    // Check quiet hours
    if (this.preferences.quietHours.enabled && this.isQuietHours()) {
      // Only allow critical notifications during quiet hours
      return notification.type === 'critical';
    }

    return true;
  }

  // Check if current time is within quiet hours
  private isQuietHours(): boolean {
    const now = new Date();
    const currentTime = `${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}`;
    
    const { start, end } = this.preferences.quietHours;
    
    if (start <= end) {
      return currentTime >= start && currentTime <= end;
    } else {
      // Quiet hours span midnight
      return currentTime >= start || currentTime <= end;
    }
  }

  // Get default channels for notification type
  private getDefaultChannels(type: NotificationType): NotificationChannel[] {
    const channels: NotificationChannel[] = ['toast'];
    
    if (this.preferences.channels.push) {
      channels.push('push');
    }
    
    if (this.preferences.channels.sound && (type === 'error' || type === 'critical')) {
      channels.push('sound');
    }
    
    if (this.preferences.channels.vibration && (type === 'error' || type === 'critical')) {
      channels.push('vibration');
    }
    
    return channels;
  }

  // Get default priority for notification type
  private getDefaultPriority(type: NotificationType): number {
    switch (type) {
      case 'critical': return 10;
      case 'error': return 8;
      case 'warning': return 6;
      case 'success': return 4;
      case 'info': return 2;
      default: return 1;
    }
  }

  // Initialize push notifications
  private async initializePushNotifications(): Promise<void> {
    if (!('Notification' in window)) {
      logger.info('Push notifications not supported');
      return;
    }

    if (Notification.permission === 'default') {
      try {
        const permission = await Notification.requestPermission();
        logger.info('Push notification permission requested', { permission });
      } catch (error) {
        logger.warn('Failed to request push notification permission', { error });
      }
    }
  }

  // Notification management
  getNotifications(filter?: {
    type?: NotificationType;
    category?: NotificationCategory;
    unreadOnly?: boolean;
    limit?: number;
  }): Notification[] {
    let filtered = [...this.notifications];

    if (filter) {
      if (filter.type) {
        filtered = filtered.filter(n => n.type === filter.type);
      }
      if (filter.category) {
        filtered = filtered.filter(n => n.category === filter.category);
      }
      if (filter.unreadOnly) {
        filtered = filtered.filter(n => !n.read);
      }
      if (filter.limit) {
        filtered = filtered.slice(0, filter.limit);
      }
    }

    return filtered;
  }

  markAsRead(id: string): void {
    const notification = this.notifications.find(n => n.id === id);
    if (notification) {
      notification.read = true;
      logger.debug('Notification marked as read', { notificationId: id });
    }
  }

  markAllAsRead(): void {
    this.notifications.forEach(n => n.read = true);
    logger.info('All notifications marked as read');
  }

  removeNotification(id: string): void {
    const index = this.notifications.findIndex(n => n.id === id);
    if (index !== -1) {
      this.notifications.splice(index, 1);
      logger.debug('Notification removed', { notificationId: id });
    }
  }

  clearAll(): void {
    this.notifications = [];
    logger.info('All notifications cleared');
  }

  // Preferences management
  updatePreferences(updates: Partial<NotificationPreferences>): void {
    this.preferences = { ...this.preferences, ...updates };
    this.savePreferences();
    logger.info('Notification preferences updated', { updates });
  }

  getPreferences(): NotificationPreferences {
    return { ...this.preferences };
  }

  // Event listeners
  addListener(listener: (notification: Notification) => void): () => void {
    this.listeners.add(listener);
    return () => this.listeners.delete(listener);
  }

  private notifyListeners(notification: Notification): void {
    this.listeners.forEach(listener => {
      try {
        listener(notification);
      } catch (error) {
        logger.error('Notification listener error', { error });
      }
    });
  }

  // Persistence
  private loadPreferences(): NotificationPreferences {
    try {
      const stored = localStorage.getItem('notification-preferences');
      if (stored) {
        return { ...DEFAULT_PREFERENCES, ...JSON.parse(stored) };
      }
    } catch (error) {
      logger.warn('Failed to load notification preferences', { error });
    }
    return DEFAULT_PREFERENCES;
  }

  private savePreferences(): void {
    try {
      localStorage.setItem('notification-preferences', JSON.stringify(this.preferences));
    } catch (error) {
      logger.warn('Failed to save notification preferences', { error });
    }
  }

  private generateId(): string {
    return `notif-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}

// Create singleton instance
export const notificationService = new NotificationService();

// Export convenience functions
export const createNotification = {
  info: (title: string, message: string, options?: any) => 
    notificationService.info(title, message, options),
  success: (title: string, message: string, options?: any) => 
    notificationService.success(title, message, options),
  warning: (title: string, message: string, options?: any) => 
    notificationService.warning(title, message, options),
  error: (title: string, message: string, options?: any) => 
    notificationService.error(title, message, options),
  critical: (title: string, message: string, options?: any) => 
    notificationService.critical(title, message, options),
};

export default notificationService;
